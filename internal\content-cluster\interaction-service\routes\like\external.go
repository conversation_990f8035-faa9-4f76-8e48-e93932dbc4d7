package like

import (
	"github.com/gin-gonic/gin"
	externalHandler "pxpat-backend/internal/content-cluster/interaction-service/external/handler"
)

func RegisterLikeExternalRoutes(r *gin.RouterGroup, handler *externalHandler.LikeHandler, authMiddleware gin.HandlerFunc) {
	likeGroup := r.Group("/like")
	{
		// 不需要认证的接口
		needAuth := likeGroup.Group("")
		needAuth.Use(authMiddleware)
		// 需要认证的接口
		needAuth.POST("/content", handler.LikeContent)   // 点赞/不喜欢或取消操作内容（统一接口）
		needAuth.GET("/my", handler.GetMyLikes)          // 获取用户点赞记录（支持类型过滤）
		needAuth.GET("/user", handler.GetOtherUserLikes) // 获取他人点赞记录
	}
}
