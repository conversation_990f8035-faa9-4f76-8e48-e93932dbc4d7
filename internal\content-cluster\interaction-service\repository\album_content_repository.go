package repository

import (
	"context"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"time"

	"gorm.io/gorm"
)

type AlbumContentRepository struct {
	db *gorm.DB
}

func NewAlbumContentRepository(db *gorm.DB) *AlbumContentRepository {
	return &AlbumContentRepository{
		db: db,
	}
}

// GetAlbumContents 获取合集内容列表
func (r *AlbumContentRepository) GetAlbumContents(ctx context.Context, albumKSUID string, offset, limit int, sortBy, sortOrder string) ([]model.AlbumContent, int64, error) {
	var contents []model.AlbumContent
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&model.AlbumContent{}).
		Where("album_ksuid = ? AND deleted_at IS NULL", albumKSUID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 构建排序
	orderClause := "sort_order ASC, created_at DESC" // 默认排序
	if sortBy != "" {
		if sortOrder == "desc" {
			orderClause = sortBy + " DESC"
		} else {
			orderClause = sortBy + " ASC"
		}
	}

	// 获取分页数据
	err := query.Order(orderClause).
		Offset(offset).
		Limit(limit).
		Find(&contents).Error

	if err != nil {
		return nil, 0, err
	}

	return contents, total, nil
}

// GetContentAlbums 获取内容所属的合集列表
func (r *AlbumContentRepository) GetContentAlbums(ctx context.Context, contentKSUID string, offset, limit int) ([]model.AlbumContent, int64, error) {
	var contents []model.AlbumContent
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&model.AlbumContent{}).
		Where("content_ksuid = ? AND deleted_at IS NULL", contentKSUID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据，按添加时间倒序
	err := query.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&contents).Error

	if err != nil {
		return nil, 0, err
	}

	return contents, total, nil
}

// CheckContentInAlbum 检查内容是否在合集中
func (r *AlbumContentRepository) CheckContentInAlbum(ctx context.Context, albumKSUID, contentKSUID string) (*model.AlbumContent, error) {
	var content model.AlbumContent
	err := r.db.WithContext(ctx).
		Where("album_ksuid = ? AND content_ksuid = ? AND deleted_at IS NULL", albumKSUID, contentKSUID).
		First(&content).Error
	if err != nil {
		return nil, err
	}
	return &content, nil
}

// UpdateContentSortOrder 更新内容在合集中的排序
func (r *AlbumContentRepository) UpdateContentSortOrder(ctx context.Context, albumKSUID, contentKSUID string, sortOrder int) error {
	return r.db.WithContext(ctx).
		Model(&model.AlbumContent{}).
		Where("album_ksuid = ? AND content_ksuid = ? AND deleted_at IS NULL", albumKSUID, contentKSUID).
		Update("sort_order", sortOrder).Error
}

// DeleteAlbumContents 删除合集的所有内容
func (r *AlbumContentRepository) DeleteAlbumContents(ctx context.Context, albumKSUID string) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&model.AlbumContent{}).
		Where("album_ksuid = ? AND deleted_at IS NULL", albumKSUID).
		Update("deleted_at", now).Error
}

// GetAlbumContentCount 获取合集内容数量
func (r *AlbumContentRepository) GetAlbumContentCount(ctx context.Context, albumKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.AlbumContent{}).
		Where("album_ksuid = ? AND deleted_at IS NULL", albumKSUID).
		Count(&count).Error
	return count, err
}

// BatchAddContentsToAlbum 批量添加内容到合集
func (r *AlbumContentRepository) BatchAddContentsToAlbum(ctx context.Context, albumContents []model.AlbumContent) error {
	if len(albumContents) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Create(&albumContents).Error
}

// BatchRemoveContentsFromAlbum 批量从合集移除内容
func (r *AlbumContentRepository) BatchRemoveContentsFromAlbum(ctx context.Context, albumKSUID string, contentKSUIDs []string) error {
	if len(contentKSUIDs) == 0 {
		return nil
	}

	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&model.AlbumContent{}).
		Where("album_ksuid = ? AND content_ksuid IN ? AND deleted_at IS NULL", albumKSUID, contentKSUIDs).
		Update("deleted_at", now).Error
}

// GetContentsByKSUIDs 根据内容KSUID列表获取合集内容记录
func (r *AlbumContentRepository) GetContentsByKSUIDs(ctx context.Context, albumKSUID string, contentKSUIDs []string) ([]model.AlbumContent, error) {
	var contents []model.AlbumContent
	err := r.db.WithContext(ctx).
		Where("album_ksuid = ? AND content_ksuid IN ? AND deleted_at IS NULL", albumKSUID, contentKSUIDs).
		Find(&contents).Error
	return contents, err
}

// GetAlbumContentsByContentType 根据内容类型获取合集内容
func (r *AlbumContentRepository) GetAlbumContentsByContentType(ctx context.Context, albumKSUID, contentType string, offset, limit int) ([]model.AlbumContent, int64, error) {
	var contents []model.AlbumContent
	var total int64

	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&model.AlbumContent{}).
		Where("album_ksuid = ? AND content_type = ? AND deleted_at IS NULL", albumKSUID, contentType)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Order("sort_order ASC, created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&contents).Error

	if err != nil {
		return nil, 0, err
	}

	return contents, total, nil
}

// GetMaxSortOrder 获取合集中的最大排序值
func (r *AlbumContentRepository) GetMaxSortOrder(ctx context.Context, albumKSUID string) (int, error) {
	var maxSortOrder int
	err := r.db.WithContext(ctx).Model(&model.AlbumContent{}).
		Where("album_ksuid = ? AND deleted_at IS NULL", albumKSUID).
		Select("COALESCE(MAX(sort_order), 0)").
		Scan(&maxSortOrder).Error
	return maxSortOrder, err
}
