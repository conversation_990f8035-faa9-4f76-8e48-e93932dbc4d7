package dto

import (
	"time"
)

// LikeContentRequest 点赞/不喜欢内容请求
type LikeContentRequest struct {
	ContentKSUID string `json:"content_ksuid" binding:"required" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"`         // 内容ID
	ContentType  string `json:"content_type" binding:"required,oneof=video short anime novel" example:"video"` // 内容类型
	Type         string `json:"type" binding:"required,oneof=like dislike" example:"like"`                     // 类型：like/dislike
}

// LikeContentResponse 点赞/不喜欢内容响应
type LikeContentResponse struct {
	Type         string `json:"type" example:"like"`         // 当前操作类型
	IsActive     bool   `json:"is_active" example:"true"`    // 是否激活（true=设置，false=取消）
	LikeCount    int64  `json:"like_count" example:"1024"`   // 喜欢总数
	DislikeCount int64  `json:"dislike_count" example:"256"` // 不喜欢总数
}

// GetUserLikesRequest 获取用户点赞记录请求
type GetUserLikesRequest struct {
	Page        int    `form:"page" json:"page" example:"1"`                                                                       // 页码，从1开始
	PageSize    int    `form:"page_size" json:"page_size" example:"20"`                                                            // 每页数量，默认20
	Type        string `form:"type" json:"type" binding:"omitempty,oneof=like dislike" example:"like"`                             // 类型过滤：like/dislike，为空则获取所有
	ContentType string `form:"content_type" json:"content_type" binding:"omitempty,oneof=video short anime novel" example:"video"` // 内容类型过滤，为空则获取所有
}

// UserLikeItem 用户点赞记录项
type UserLikeItem struct {
	LikeKSUID    string    `json:"like_ksuid" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"`
	ContentKSUID string    `json:"content_ksuid" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"`
	ContentType  string    `json:"content_type" example:"video"`
	Type         string    `json:"type" example:"like"` // 类型：like/dislike
	CreatedAt    time.Time `json:"created_at" example:"2023-01-01T00:00:00Z"`
}

// GetUserLikesResponse 获取用户点赞记录响应
type GetUserLikesResponse struct {
	Likes      []UserLikeItem `json:"likes"`
	Total      int64          `json:"total" example:"100"`
	Page       int            `json:"page" example:"1"`
	PageSize   int            `json:"page_size" example:"20"`
	TotalPages int            `json:"total_pages" example:"5"`
	HasNext    bool           `json:"has_next" example:"true"`
	HasPrev    bool           `json:"has_prev" example:"false"`
}

// GetOtherUserLikesRequest 获取他人点赞记录请求
type GetOtherUserLikesRequest struct {
	UserKSUID   string `form:"user_ksuid" json:"user_ksuid" binding:"required" example:"01ARZ3NDEKTSV4RRFFQ69G5FAV"`               // 目标用户ID
	Page        int    `form:"page" json:"page" example:"1"`                                                                       // 页码，从1开始
	PageSize    int    `form:"page_size" json:"page_size" example:"20"`                                                            // 每页数量，默认20
	Type        string `form:"type" json:"type" binding:"omitempty,oneof=like dislike" example:"like"`                             // 类型过滤：like/dislike，为空则获取所有
	ContentType string `form:"content_type" json:"content_type" binding:"omitempty,oneof=video short anime novel" example:"video"` // 内容类型过滤，为空则获取所有
}

// ========== 内部服务调用相关 DTO ==========

// ContentLikeStatsItem 内容点赞统计项
type ContentLikeStatsItem struct {
	ContentKSUID string `json:"content_ksuid"`
	LikeCount    int64  `json:"like_count"`
	DislikeCount int64  `json:"dislike_count"`
}

// UserLikeStatusItem 用户点赞状态项
type UserLikeStatusItem struct {
	Type       string `json:"type"`        // 点赞类型：like/dislike，空字符串表示未点赞
	IsLiked    bool   `json:"is_liked"`    // 是否点赞
	IsDisliked bool   `json:"is_disliked"` // 是否不喜欢
}

// CheckUserLikeStatusRequest 检查用户点赞状态请求
type CheckUserLikeStatusRequest struct {
	UserKSUID    string `form:"user_ksuid" binding:"required"`
	ContentKSUID string `form:"content_ksuid" binding:"required"`
}

// CheckUserLikeStatusResponse 检查用户点赞状态响应
type CheckUserLikeStatusResponse struct {
	LikeType string `json:"like_type"` // 点赞类型：like/dislike，空字符串表示未点赞
	Exists   bool   `json:"exists"`    // 是否存在点赞记录
}

// GetContentLikeCountsRequest 获取内容点赞统计请求
type GetContentLikeCountsRequest struct {
	ContentKSUID string `form:"content_ksuid" binding:"required"`
}

// GetContentLikeCountsResponse 获取内容点赞统计响应
type GetContentLikeCountsResponse struct {
	LikeCount    int64 `json:"like_count"`
	DislikeCount int64 `json:"dislike_count"`
}

// BatchGetContentLikeStatsRequest 批量获取内容点赞统计请求
type BatchGetContentLikeStatsRequest struct {
	ContentKSUIDs []string `json:"content_ksuids" binding:"required"`
}

// BatchGetContentLikeStatsResponse 批量获取内容点赞统计响应
type BatchGetContentLikeStatsResponse struct {
	Stats map[string]ContentLikeStatsItem `json:"stats"`
}

// BatchCheckUserLikeStatusRequest 批量检查用户点赞状态请求
type BatchCheckUserLikeStatusRequest struct {
	UserKSUID     string   `json:"user_ksuid" binding:"required"`
	ContentKSUIDs []string `json:"content_ksuids" binding:"required"`
}

// BatchCheckUserLikeStatusResponse 批量检查用户点赞状态响应
type BatchCheckUserLikeStatusResponse struct {
	Statuses map[string]UserLikeStatusItem `json:"statuses"`
}
