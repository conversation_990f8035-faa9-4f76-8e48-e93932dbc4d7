package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"pxpat-backend/internal/content-cluster/video-service/dto"
	"pxpat-backend/internal/content-cluster/video-service/external/service"
	"pxpat-backend/internal/content-cluster/video-service/model"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	"pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"
	globalTypes "pxpat-backend/pkg/types"
)

type ContentHandler struct {
	contentService *service.ContentService
}

func NewContentHandler(
	contentService *service.ContentService,
) *ContentHandler {
	return &ContentHandler{
		contentService: contentService,
	}
}

func (h *ContentHandler) PublishVideo(c *gin.Context) {
	// 创建子span，使用组件级别的tracer
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "PublishVideo")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	UserKSUID := ksuid.GetKSUID(c)

	// 添加span属性
	opentelemetry.AddAttribute(span, "user_ksuid", UserKSUID)
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "client_ip", c.ClientIP())

	// 记录日志（包含trace信息）
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", UserKSUID).
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Str("client_ip", c.ClientIP()).
		Msg("收到发布视频请求")

	// 添加链路追踪事件
	opentelemetry.AddEvent(span, "request_start")

	// 解析发布视频的元信息
	var req dto.PublishVideoRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Str("user_ksuid", UserKSUID).
			Msg("解析发布视频请求参数失败")

		// 添加错误到链路追踪
		opentelemetry.AddError(span, err)
		opentelemetry.AddEvent(span, "parameter_binding_failed")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 添加请求参数到span属性
	opentelemetry.AddAttribute(span, "video_id", req.VideoID)
	opentelemetry.AddAttribute(span, "title", req.Title)
	opentelemetry.AddEvent(span, "parameter_binding_success")

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Interface("传递参数", req).
		Msg("解析发布视频请求参数成功")

	// 判断上传的必填项是否为空
	gErr := dto.ValidatePublishVideoRequest(&req)
	if gErr != nil {
		log.Warn().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(gErr).
			Str("user_ksuid", UserKSUID).
			Str("video_id", req.VideoID).
			Str("title", req.Title).
			Msg("发布视频参数校验失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("content_type", req.Type).
		Msg("开始调用 VideoService 发布内容")

	opentelemetry.AddEvent(span, "service_call_start")

	var contentRecord *model.Content

	// 根据 type 字段路由到不同的 service 方法
	switch req.Type {
	case "anime":
		// 转换为 PublishAnimeRequest
		animeReq := &dto.PublishAnimeRequest{
			Title:             req.Title,
			Bio:               req.Bio,
			Category:          req.Category,
			OriginalTags:      req.OriginalTags,
			VideoID:           req.VideoID,
			FileKSUID:         req.FileKSUID,
			SourceType:        req.SourceType,
			CoverKSUID:        req.CoverKSUID,
			Collaborators:     req.Collaborators,
			PublishAt:         req.PublishAt,
			CreationTime:      req.CreationTime,
			AutoPushAudit:     req.AutoPushAudit,
			TempCreationTime:  req.TempCreationTime,
			SplitMainCreators: req.SplitMainCreators,
			SplitMembers:      req.SplitMembers,
		}
		contentRecord, gErr = h.contentService.PublishAnime(c.Request.Context(), UserKSUID, animeReq)
	case "short":
		// TODO: 实现 PublishShort 方法
		log.Warn().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("content_type", req.Type).
			Msg("短视频发布功能暂未实现")
		c.JSON(http.StatusNotImplemented, globalTypes.GlobalResponse{
			Code: errors.SERVICE_UNAVAILABLE,
		})
		return
	case "video":
		// 默认为视频类型
		contentRecord, gErr = h.contentService.PublishVideo(c.Request.Context(), UserKSUID, &req)
	default:
		log.Warn().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("content_type", req.Type).
			Msg("不支持的内容类型")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.TYPE_ERROR,
		})
		return
	}
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(gErr.Err).
			Int("internal_error_code", gErr.InternalCode).
			Str("user_ksuid", UserKSUID).
			Str("video_id", req.VideoID).
			Str("title", req.Title).
			Str("content_type", req.Type).
			Msg("发布内容失败")

		// 添加错误到链路追踪
		opentelemetry.AddError(span, gErr)
		opentelemetry.AddEvent(span, "service_call_failed")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", UserKSUID).
		Str("video_ksuid", contentRecord.ContentKSUID).
		Str("video_id", req.VideoID).
		Str("title", req.Title).
		Str("content_type", req.Type).
		Msg("发布内容成功")

	// 添加成功事件到链路追踪
	opentelemetry.AddEvent(span, "service_call_success")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: contentRecord,
	})
}

func (h *ContentHandler) GetContentByContentKSUID(c *gin.Context) {
	// 创建子span，使用组件级别的tracer
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetContentByContentKSUID")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 获取参数,并且校验
	ContentID := c.Param("content_ksuid")

	// 获取用户KSUID（可能为空，表示未登录）
	userKSUID, _ := ksuid.TryGetKSUID(c)

	// 添加span属性
	opentelemetry.AddAttribute(span, "content_id", ContentID)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "client_ip", c.ClientIP())

	// 记录日志（包含trace信息）
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("content_id", ContentID).
		Str("user_ksuid", userKSUID).
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Str("client_ip", c.ClientIP()).
		Msg("收到获取内容详情请求")

	// 添加链路追踪事件
	opentelemetry.AddEvent(span, "request_start")

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Msg("开始调用 VideoService.GetContentByContentKSUID")

	opentelemetry.AddEvent(span, "service_call_start")

	var response *dto.ContentDetailResponse
	var gErr *errors.Errors

	// 如果用户已登录，使用包含用户交互状态的方法
	if userKSUID != "" {
		response, gErr = h.contentService.GetContentDetailWithUserInteraction(c.Request.Context(), ContentID, userKSUID)
		if gErr != nil {
			log.Error().
				Str("trace_id", traceID).
				Str("span_id", spanID).
				Err(gErr).
				Int("internal_error_code", gErr.InternalCode).
				Str("content_id", ContentID).
				Str("user_ksuid", userKSUID).
				Msg("获取包含用户交互状态的内容详情失败")

			// 添加错误到链路追踪
			opentelemetry.AddError(span, gErr)
			opentelemetry.AddEvent(span, "service_call_failed")

			c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
				Code: gErr.Code,
			})
			return
		}
	} else {
		// 用户未登录，获取基础内容信息
		content, err := h.contentService.GetContentByContentKSUID(c.Request.Context(), ContentID)
		if err != nil {
			log.Error().
				Str("trace_id", traceID).
				Str("span_id", spanID).
				Err(err).
				Int("internal_error_code", err.InternalCode).
				Str("content_id", ContentID).
				Msg("获取内容详情失败")

			// 添加错误到链路追踪
			opentelemetry.AddError(span, err)
			opentelemetry.AddEvent(span, "service_call_failed")

			c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
				Code: err.Code,
			})
			return
		}

		// 构建响应
		response = &dto.ContentDetailResponse{
			ExportContentModel: content.ExportContentModel,
			UserInfo:           content.UserInfo,
			Collaborators:      content.Collaborators,
		}
	}

	if userKSUID != "" {
		log.Info().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("video_id", response.VideoID).
			Str("title", response.Title).
			Str("status", string(response.Status)).
			Str("user_ksuid", userKSUID).
			Bool("is_liked", response.IsLiked).
			Bool("is_disliked", response.IsDisliked).
			Bool("is_favorited", response.IsFavorited).
			Msg("获取内容详情成功（含用户交互状态）")
	} else {
		log.Info().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("video_id", response.VideoID).
			Str("title", response.Title).
			Str("status", string(response.Status)).
			Msg("获取内容详情成功（未登录用户）")
	}

	// 添加成功事件到链路追踪
	opentelemetry.AddEvent(span, "service_call_success")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetPublishedContentsByType 根据类型获取已发布内容列表
func (h *ContentHandler) GetPublishedContentsByType(c *gin.Context) {
	log.Info().
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Str("query", c.Request.URL.RawQuery).
		Msg("收到按类型获取已发布内容列表请求")

	// 绑定查询参数
	var req dto.GetPublishedContentsByTypeRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Err(err).
			Msg("解析按类型获取已发布内容列表请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
	if req.SortBy == "" {
		req.SortBy = "latest" // 默认按最新发布排序
	}

	log.Info().
		Str("content_type", req.Type).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("sort_by", string(req.SortBy)).
		Str("source_type", req.SourceType).
		Str("user_ksuid", req.UserKSUID).
		Interface("tag_ids", req.TagIDs).
		Uint("category_id", req.CategoryID).
		Str("level", req.Level).
		Msg("开始按类型获取已发布内容列表")

	// 调用服务层
	contents, total, gErr := h.contentService.GetPublishedContentsByType(
		c.Request.Context(),
		req.Type,
		req.Page,
		req.PageSize,
		string(req.SortBy),
		req.SourceType,
		req.UserKSUID,
		req.TagIDs,
		req.CategoryID,
		req.Level,
	)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("content_type", req.Type).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Str("sort_by", string(req.SortBy)).
			Str("source_type", req.SourceType).
			Str("user_ksuid", req.UserKSUID).
			Interface("tag_ids", req.TagIDs).
			Uint("category_id", req.CategoryID).
			Str("level", req.Level).
			Msg("按类型获取已发布内容列表失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	// 计算分页信息
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	hasNext := req.Page < totalPages
	hasPrev := req.Page > 1

	// 构建响应
	response := dto.GetPublishedContentsByTypeResponse{
		Contents:   contents,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	}

	log.Info().
		Str("content_type", req.Type).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Int64("total", total).
		Int("count", len(contents)).
		Int("total_pages", totalPages).
		Msg("按类型获取已发布内容列表成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetMyContents 获取自己的投稿内容
func (h *ContentHandler) GetMyContents(c *gin.Context) {
	log.Info().
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Str("query", c.Request.URL.RawQuery).
		Msg("收到获取自己投稿内容请求")

	// 获取用户KSUID
	userKSUID := ksuid.GetKSUID(c)

	// 绑定查询参数
	var req dto.GetMyContentsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Err(err).
			Msg("解析获取自己投稿内容请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("content_type", req.ContentType).
		Msg("开始获取用户投稿内容列表")

	// 调用服务层
	contents, total, gErr := h.contentService.GetMyContents(
		c.Request.Context(),
		userKSUID,
		req.Page,
		req.PageSize,
		req.ContentType,
	)
	if gErr != nil {
		log.Error().
			Err(gErr.Err).
			Str("user_ksuid", userKSUID).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Str("content_type", req.ContentType).
			Msg("获取用户投稿内容列表失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	// 计算分页信息
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	hasNext := req.Page < totalPages
	hasPrev := req.Page > 1

	// 构建响应
	response := &dto.GetMyContentsResponse{
		Contents:   contents,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("content_type", req.ContentType).
		Int64("total", total).
		Int("count", len(contents)).
		Int("total_pages", totalPages).
		Msg("获取用户投稿内容列表成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetRandomVideos 随机获取视频
func (h *ContentHandler) GetRandomVideos(c *gin.Context) {
	log.Info().
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Str("query", c.Request.URL.RawQuery).
		Msg("收到随机获取视频请求")

	// 绑定查询参数
	var req dto.GetRandomVideosRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Err(err).
			Msg("解析随机获取视频请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 设置默认值
	if req.Size <= 0 || req.Size > 100 {
		req.Size = 20
	}

	log.Info().
		Uint("tag_id", req.TagID).
		Uint("category_id", req.CategoryID).
		Str("level", req.Level).
		Str("orientation", req.Orientation).
		Int("size", req.Size).
		Msg("开始随机获取视频")

	// 调用服务层
	videos, gErr := h.contentService.GetRandomVideos(
		c.Request.Context(),
		req.TagID,
		req.CategoryID,
		req.Level,
		req.Orientation,
		req.Size,
	)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Uint("tag_id", req.TagID).
			Uint("category_id", req.CategoryID).
			Str("level", req.Level).
			Str("orientation", req.Orientation).
			Int("size", req.Size).
			Msg("随机获取视频失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	count := len(videos)

	// 构建响应
	response := dto.GetRandomVideosResponse{
		Videos: videos,
		Count:  count,
	}

	log.Info().
		Uint("tag_id", req.TagID).
		Uint("category_id", req.CategoryID).
		Str("level", req.Level).
		Str("orientation", req.Orientation).
		Int("size", req.Size).
		Int("count", count).
		Msg("随机获取视频成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}
