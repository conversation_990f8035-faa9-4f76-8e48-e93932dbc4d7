package model

import (
	"gorm.io/gorm"
	"pxpat-backend/pkg/ksuid"
	"time"
)

// ContentStatus 内容状态枚举
type ContentStatus string

const (
	StatusDraft       ContentStatus = "draft"       // 草稿
	StatusPending     ContentStatus = "pending"     // 待审核
	StatusAudit       ContentStatus = "audit"       // 审核中
	StatusTranscoding ContentStatus = "transcoding" // 转码中
	StatusPublished   ContentStatus = "published"   // 已发布
	StatusRejected    ContentStatus = "rejected"    // 审核拒绝
	StatusArchived    ContentStatus = "archived"    // 已下架
)

// ContentType 内容类型枚举
type ContentType string

const (
	TypeVideo      ContentType = "video"       // 视频
	TypeShortVideo ContentType = "short_video" // 短视频
	TypeAnime      ContentType = "anime"       // 动漫
	TypeManga      ContentType = "manga"       // 漫画
	TypeNovel      ContentType = "novel"       // 小说
	TypeMusic      ContentType = "music"       // 音乐
	TypeArticle    ContentType = "article"     // 文章
	TypeDrama      ContentType = "drama"       // 短剧
)

// ContentRating 内容分级枚举
type ContentRating string

const (
	RatingA ContentRating = "A" // 正常内容
	RatingB ContentRating = "B" // 隐私内容
	RatingC ContentRating = "C" // 全部内容（包含A和B）
)

// ContentSourceType 内容来源类型枚举
type ContentSourceType string

const (
	SourceOriginal ContentSourceType = "original"      // 原创
	SourceShared   ContentSourceType = "transshipment" // 转载
)

// Content 内容基础模型
type Content struct {
	// 基础信息
	ContentKSUID string        `gorm:"column:content_ksuid;primaryKey;type:varchar(32);not null" json:"content_ksuid"`
	Title        string        `gorm:"type:varchar(200);not null" json:"title"`                       // 标题
	Description  string        `gorm:"type:text" json:"description"`                                  // 描述
	Type         ContentType   `gorm:"type:varchar(20);not null;index" json:"type"`                   // 内容类型
	Status       ContentStatus `gorm:"type:varchar(20);not null;index;default:'draft'" json:"status"` // 内容状态

	// 视频编号和制作信息
	VideoID string `gorm:"type:varchar(50);uniqueIndex" json:"video_ID"` // 视频编号

	// 创作者信息
	CreatorType           string `gorm:"type:varchar(20);not null" json:"creator_type"`                       // 创作者类型
	UserKSUID             string `gorm:"column:user_ksuid;type:varchar(32);not null;index" json:"user_ksuid"` // 发布这个视频的用户KSUID
	OriginalCollaborators string `gorm:"type:text" json:"original_collaborators,omitempty"`                   // 原始协作者信息（JSON格式）

	// 向外的播放信息(从minio解析的url存储在此,每次取时判断是否过期)
	Cover        string  `gorm:"type:text" json:"cover"`                        // 封面在存储桶中的路径
	CoverURL     string  `gorm:"type:text" json:"cover_url"`                    // 封面公开访问URL
	PlayURL      string  `gorm:"type:text" json:"play_url"`                     // 播放url,从Plays解析
	KeyFramesURL string  `gorm:"type:text" json:"key_frames_url"`               // 关键帧url,从KeyFrames解析
	PreviewURL   string  `gorm:"type:text" json:"preview_url"`                  // 预览视频url,从PreviewVideo解析
	Duration     float64 `json:"duration,omitempty"`                            // 时长
	Language     string  `gorm:"type:varchar(10);default:''" json:"language"`   // 语言
	Orientation  string  `gorm:"type:varchar(20)" json:"orientation,omitempty"` // 视频方向 (landscape/portrait)

	// 新增：媒体处理结果数据（从media-processing-service接收）
	Plays        string `gorm:"type:text" json:"-"` // DASH播放路径 (JSON格式)
	KeyFrames    string `gorm:"type:text" json:"-"` // 关键帧路径数组 (JSON格式)
	PreviewVideo string `gorm:"type:text" json:"-"` // 预览视频路径

	// 新增：视频文件URL信息
	FileKSUID string `gorm:"column:file_ksuid;type:varchar(256);not null;uniqueIndex" json:"file_ksuid"` // 视频文件ID

	// 媒体处理相关信息
	MediaResultID int64 `gorm:"type:bigint;index" json:"media_result_id,omitempty"` // 媒体处理任务ID

	// 内容来源
	SourceType       ContentSourceType `gorm:"type:varchar(20);not null;default:'original'" json:"source_type"` // 内容来源类型
	OriginalURL      string            `gorm:"type:varchar(1000)" json:"original_url"`                          // 原作地址
	OriginalPlatform string            `gorm:"type:varchar(50)" json:"original_platform"`                       // 原作平台
	OriginalIcon     string            `gorm:"type:varchar(50)" json:"original_icon"`                           // 原作平台图标
	// 后期加上功能 根据链接自动识别平台来源 并填充以上字段

	// 地区控制
	AllowedRegions    string `gorm:"type:text" json:"allowed_regions,omitempty"`    // 可用地区
	RestrictedRegions string `gorm:"type:text" json:"restricted_regions,omitempty"` // 限制地区

	// 新增：时间管理
	CreationTime time.Time `json:"creation_time"` // 创作时间（与系统创建时间分离）
	PublishAt    time.Time `json:"publish_at"`    // 计划发布时间

	// 统计信息
	ViewCount      int64 `gorm:"default:0" json:"view_count"`      // 浏览量
	CommentCount   int64 `gorm:"default:0" json:"comment_count"`   // 评论数
	ComplaintCount int64 `gorm:"default:0" json:"complaint_count"` // 投诉数

	// 版权信息
	CopyrightInfo string    `gorm:"type:text" json:"copyright_info,omitempty"` // 版权信息
	ExpiresAt     time.Time `json:"expires_at,omitempty"`                      // 版权到期时间

	// 审核
	AuditTaskID           uint64 `gorm:"not null;index" json:"audit_task_id"`
	IsTranscodeAuditVideo bool   `gorm:"default:false" json:"is_transcode_audit_video"` // 是否已经转码审核专用视频
	Level                 string `gorm:"type:varchar(1);index" json:"level"`            // 审核级别 (A/B/C)

	// 标签相关
	OriginalTags string `gorm:"type:text" json:"original_tags,omitempty"` // 原始标签（格式：标签1,标签2,标签3）
	Tags         []Tag  `gorm:"many2many:video_content_tags;foreignKey:ContentKSUID;joinForeignKey:content_ksuid;references:ID;joinReferences:tag_id" json:"tags,omitempty"`

	// 关联关系
	CategoryID uint     `gorm:"not null;index" json:"category_id"`                             // 外键字段
	Category   Category `gorm:"foreignKey:CategoryID;references:ID" json:"category,omitempty"` // 引用分类对象

	// 用户角色关联关系
	Collaborators []Collaborator `gorm:"foreignKey:ContentKSUID;references:ContentKSUID" json:"collaborators,omitempty"`

	// 协作相关
	HasCollaborators bool `gorm:"default:false;index" json:"has_collaborators"` // 是否有协作者
	AllApproved      bool `gorm:"default:false;index" json:"all_approved"`      // 所有协作者是否都已同意
	AutoPushAudit    bool `gorm:"default:false;index" json:"auto_push_audit"`   // 是否自动推送到审核

	// 基础字段
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// TableName 指定表名
func (*Content) TableName() string {
	return "video_contents"
}

// BeforeCreate GORM钩子，在创建记录前自动生成KSUID
func (c *Content) BeforeCreate(tx *gorm.DB) error {
	if c.ContentKSUID == "" {
		c.ContentKSUID = ksuid.GenerateKSUID()
	}
	return nil
}
