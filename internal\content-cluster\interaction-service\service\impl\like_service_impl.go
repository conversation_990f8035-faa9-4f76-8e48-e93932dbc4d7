package impl

import (
	"context"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/client"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	externalService "pxpat-backend/internal/content-cluster/interaction-service/external/service"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	"pxpat-backend/pkg/errors"
)

// likeServiceImpl 点赞服务实现
type likeServiceImpl struct {
	likeRepo          repository.LikeRepository
	userServiceClient client.UserServiceClient
}

// NewLikeService 创建点赞服务实例
func NewLikeService(likeRepo repository.LikeRepository, userServiceClient client.UserServiceClient) externalService.LikeService {
	return &likeServiceImpl{
		likeRepo:          likeRepo,
		userServiceClient: userServiceClient,
	}
}

// LikeContent 点赞/不喜欢或取消操作内容
func (s *likeServiceImpl) LikeContent(ctx context.Context, userKSUID string, req *dto.LikeContentRequest) (*dto.LikeContentResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Str("type", req.Type).
		Msg("开始处理内容点赞/不喜欢")

	// 验证类型参数
	var likeType model.LikeType
	switch req.Type {
	case "like":
		likeType = model.LikeTypeLike
	case "dislike":
		likeType = model.LikeTypeDislike
	default:
		log.Error().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("type", req.Type).
			Msg("无效的类型参数")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, nil)
	}

	// 验证内容类型参数
	var contentType model.ContentType
	switch req.ContentType {
	case "video":
		contentType = model.ContentTypeVideo
	case "short":
		contentType = model.ContentTypeShort
	case "anime":
		contentType = model.ContentTypeAnime
	case "novel":
		contentType = model.ContentTypeNovel
	default:
		log.Error().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Msg("无效的内容类型参数")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, nil)
	}

	// 执行点赞/不喜欢或取消操作
	isActive, err := s.likeRepo.LikeContent(ctx, userKSUID, req.ContentKSUID, likeType, contentType)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("type", req.Type).
			Msg("处理内容点赞/不喜欢失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.UPDATE_RECORD_ERROR, err)
	}

	// 获取最新的统计数据
	likeCount, dislikeCount, err := s.likeRepo.GetLikeCounts(ctx, req.ContentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("获取点赞统计失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	response := &dto.LikeContentResponse{
		Type:         req.Type,
		IsActive:     isActive,
		LikeCount:    likeCount,
		DislikeCount: dislikeCount,
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("type", req.Type).
		Bool("is_active", isActive).
		Int64("like_count", likeCount).
		Int64("dislike_count", dislikeCount).
		Msg("处理内容点赞/不喜欢成功")

	return response, nil
}

// GetUserLikes 获取用户点赞记录（支持类型过滤）
func (s *likeServiceImpl) GetUserLikes(ctx context.Context, userKSUID string, req *dto.GetUserLikesRequest) (*dto.GetUserLikesResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("type", req.Type).
		Str("content_type", req.ContentType).
		Msg("开始获取用户点赞记录")

	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 处理类型过滤
	var likeType *model.LikeType
	if req.Type != "" {
		switch req.Type {
		case "like":
			t := model.LikeTypeLike
			likeType = &t
		case "dislike":
			t := model.LikeTypeDislike
			likeType = &t
		default:
			log.Error().
				Str("user_ksuid", userKSUID).
				Str("type", req.Type).
				Msg("无效的类型参数")
			return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, nil)
		}
	}

	// 获取用户点赞记录
	likes, total, err := s.likeRepo.GetUserLikes(ctx, userKSUID, req.Page, req.PageSize, likeType, req.ContentType)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Str("type", req.Type).
			Str("content_type", req.ContentType).
			Msg("获取用户点赞记录失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 转换为DTO
	likeItems := make([]dto.UserLikeItem, len(likes))
	for i, like := range likes {
		likeItems[i] = dto.UserLikeItem{
			LikeKSUID:    like.LikeKSUID,
			ContentKSUID: like.ContentKSUID,
			ContentType:  string(like.ContentType),
			Type:         string(like.Type),
			CreatedAt:    like.CreatedAt,
		}
	}

	// 计算分页信息
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	hasNext := req.Page < totalPages
	hasPrev := req.Page > 1

	response := &dto.GetUserLikesResponse{
		Likes:      likeItems,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("type", req.Type).
		Str("content_type", req.ContentType).
		Int64("total", total).
		Int("count", len(likeItems)).
		Msg("获取用户点赞记录成功")

	return response, nil
}

// GetOtherUserLikes 获取他人点赞记录
func (s *likeServiceImpl) GetOtherUserLikes(ctx context.Context, req *dto.GetOtherUserLikesRequest) (*dto.GetUserLikesResponse, *errors.Errors) {
	log.Info().
		Str("target_user_ksuid", req.UserKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("type", req.Type).
		Str("content_type", req.ContentType).
		Msg("开始获取他人点赞记录")

	// 参数校验
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 获取目标用户的完整信息（包含隐私设置）
	userFullInfo, err := s.userServiceClient.GetUserFullInfo(req.UserKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("target_user_ksuid", req.UserKSUID).
			Msg("获取目标用户信息失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 检查目标用户是否设置了点赞记录隐私
	if userFullInfo.SecretLike {
		log.Info().
			Str("target_user_ksuid", req.UserKSUID).
			Msg("目标用户设置了点赞记录隐私，拒绝访问")
		return nil, errors.NewGlobalErrors(errors.SECRET_USER_LIKE, errors.SECRET_USER_LIKE, nil)
	}

	// 调用获取用户点赞记录的方法
	getUserLikesReq := &dto.GetUserLikesRequest{
		Page:        req.Page,
		PageSize:    req.PageSize,
		Type:        req.Type,
		ContentType: req.ContentType,
	}
	return s.GetUserLikes(ctx, req.UserKSUID, getUserLikesReq)
}
