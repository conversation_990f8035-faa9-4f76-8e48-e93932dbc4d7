package album

import (
	"github.com/gin-gonic/gin"
	externalHandler "pxpat-backend/internal/content-cluster/interaction-service/external/handler"
	"pxpat-backend/pkg/auth"
	auth2 "pxpat-backend/pkg/middleware/auth"
)

func RegisterAlbumExternalRoutes(
	r *gin.RouterGroup,
	handler *externalHandler.AlbumHandler,
	jwtManager *auth.Manager,
) {
	albumsGroup := r.Group("/albums")

	// 需要认证的路由
	needAuthGroup := albumsGroup.Group("")
	needAuthGroup.Use(auth2.UserAuthMiddleware(*jwtManager))
	{
		// 合集基础操作
		needAuthGroup.POST("", handler.CreateAlbum)                // 创建合集
		needAuthGroup.PUT("/:album_ksuid", handler.UpdateAlbum)    // 更新合集信息
		needAuthGroup.DELETE("/:album_ksuid", handler.DeleteAlbum) // 删除合集

		// 批量操作
		needAuthGroup.POST("/contents/batch-add", handler.BatchAddContents)       // 批量添加内容
		needAuthGroup.POST("/contents/batch-remove", handler.BatchRemoveContents) // 批量移除内容
	}

	// 公开路由（可选认证，用于权限检查）
	{
		albumsGroup.GET("/:album_ksuid", handler.GetAlbum)                  // 获取合集信息
		albumsGroup.GET("/:album_ksuid/contents", handler.GetAlbumContents) // 获取合集内容列表
		albumsGroup.GET("/user/:user_ksuid", handler.GetUserAlbums)         // 获取用户合集列表
	}
}
