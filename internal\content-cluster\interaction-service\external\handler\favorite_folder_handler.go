package handler

import (
	"net/http"
	"pxpat-backend/pkg/errors"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/external/service"
	"pxpat-backend/pkg/ksuid"
	globalTypes "pxpat-backend/pkg/types"
)

// FavoriteFolderHandler 收藏夹处理器
type FavoriteFolderHandler struct {
	favoriteFolderService *service.FavoriteFolderService
}

// NewFavoriteFolderHandler 创建收藏夹处理器
func NewFavoriteFolderHandler(favoriteFolderService *service.FavoriteFolderService) *FavoriteFolderHandler {
	return &FavoriteFolderHandler{
		favoriteFolderService: favoriteFolderService,
	}
}

// CreateFolder 创建收藏夹
func (h *FavoriteFolderHandler) CreateFolder(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	var req dto.CreateFavoriteFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("Invalid request parameters for create folder")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    errors.INVALID_PARAMETER,
			Message: "请求参数无效",
		})
		return
	}

	// 参数验证
	if req.DirName == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "收藏夹名称不能为空",
		})
		return
	}

	folder, err := h.favoriteFolderService.CreateFolder(c.Request.Context(), userKSUID, &req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_name", req.DirName).
			Msg("Failed to create favorite folder")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "创建收藏夹失败",
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folder.FavoriteFolderID).
		Str("folder_name", folder.DirName).
		Msg("Successfully created favorite folder")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "创建收藏夹成功",
		Data:    folder,
	})
}

// UpdateFolder 更新收藏夹
func (h *FavoriteFolderHandler) UpdateFolder(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)
	folderID := c.Param("folder_id")

	if folderID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "收藏夹ID不能为空",
		})
		return
	}

	var req dto.UpdateFavoriteFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("Invalid request parameters for update folder")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "请求参数无效",
		})
		return
	}

	folder, err := h.favoriteFolderService.UpdateFolder(c.Request.Context(), userKSUID, folderID, &req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("Failed to update favorite folder")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "更新收藏夹失败",
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folderID).
		Msg("Successfully updated favorite folder")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "更新收藏夹成功",
		Data:    folder,
	})
}

// DeleteFolder 删除收藏夹
func (h *FavoriteFolderHandler) DeleteFolder(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)
	folderID := c.Param("folder_id")

	if folderID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "收藏夹ID不能为空",
		})
		return
	}

	err := h.favoriteFolderService.DeleteFolder(c.Request.Context(), userKSUID, folderID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("Failed to delete favorite folder")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "删除收藏夹失败",
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("folder_id", folderID).
		Msg("Successfully deleted favorite folder")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "删除收藏夹成功",
	})
}

// GetFolder 获取收藏夹详情
func (h *FavoriteFolderHandler) GetFolder(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)
	folderID := c.Param("folder_id")

	if folderID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "收藏夹ID不能为空",
		})
		return
	}

	folder, err := h.favoriteFolderService.GetFolder(c.Request.Context(), userKSUID, folderID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_id", folderID).
			Msg("Failed to get favorite folder")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "获取收藏夹失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "获取收藏夹成功",
		Data:    folder,
	})
}

// GetFolders 获取收藏夹列表
func (h *FavoriteFolderHandler) GetFolders(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	// 解析查询参数
	var req dto.GetFavoriteFoldersRequest
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}
	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 {
			req.PageSize = pageSize
		}
	}
	req.IncludeItemCount = c.Query("include_item_count") == "true"

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	response, err := h.favoriteFolderService.GetFolders(c.Request.Context(), userKSUID, &req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("Failed to get favorite folders")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "获取收藏夹列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "获取收藏夹列表成功",
		Data:    response,
	})
}

// GetFavoriteStats 获取收藏统计
func (h *FavoriteFolderHandler) GetFavoriteStats(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	stats, err := h.favoriteFolderService.GetFavoriteStats(c.Request.Context(), userKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("Failed to get favorite stats")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "获取收藏统计失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "获取收藏统计成功",
		Data:    stats,
	})
}
