package impl

import (
	"context"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	intraService "pxpat-backend/internal/content-cluster/interaction-service/intra/service"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	"pxpat-backend/pkg/errors"
)

type InternalPlayHistoryServiceImpl struct {
	playHistoryRepo repository.PlayHistoryRepository
}

func NewInternalPlayHistoryService(
	playHistoryRepo repository.PlayHistoryRepository,
) intraService.InternalPlayHistoryService {
	return &InternalPlayHistoryServiceImpl{
		playHistoryRepo: playHistoryRepo,
	}
}

// UpdatePlayHistory 内部更新播放历史记录
func (s *InternalPlayHistoryServiceImpl) UpdatePlayHistory(ctx context.Context, req *dto.InternalUpdatePlayHistoryRequest) (*dto.InternalUpdatePlayHistoryResponse, error) {
	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Int64("play_duration", req.PlayDuration).
		Msg("开始内部更新播放历史记录")

	// 验证播放时长不能为负数
	if req.PlayDuration < 0 {
		log.Error().
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Int64("play_duration", req.PlayDuration).
			Msg("播放时长不能为负数")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, nil)
	}

	// 验证内容类型
	if !model.IsValidContentType(model.ContentType(req.ContentType)) {
		log.Error().
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Msg("无效的内容类型")
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, nil)
	}

	// 更新播放历史记录
	playHistory, isNew, err := s.playHistoryRepo.UpdatePlayHistory(ctx, req.UserKSUID, req.ContentKSUID, req.ContentType, req.PlayDuration)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Int64("play_duration", req.PlayDuration).
			Msg("内部更新播放历史记录失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.UPDATE_RECORD_ERROR, err)
	}

	// 构建响应
	response := &dto.InternalUpdatePlayHistoryResponse{
		PlayHistoryItemID: playHistory.PlayHistoryItemID,
		UserKSUID:         playHistory.UserKSUID,
		ContentKSUID:      playHistory.ContentKSUID,
		ContentType:       string(playHistory.ContentType),
		PlayDuration:      playHistory.PlayDuration,
		IsNew:             isNew,
	}

	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Int64("play_duration", req.PlayDuration).
		Str("play_history_item_id", playHistory.PlayHistoryItemID).
		Bool("is_new", isNew).
		Msg("内部更新播放历史记录成功")

	return response, nil
}

// GetPlayHistory 内部获取播放历史记录
func (s *InternalPlayHistoryServiceImpl) GetPlayHistory(ctx context.Context, req *dto.InternalGetPlayHistoryRequest) (*dto.InternalGetPlayHistoryResponse, error) {
	log.Debug().
		Str("user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("开始内部获取播放历史记录")

	// 获取播放历史记录
	playHistory, err := s.playHistoryRepo.GetByUserAndContent(ctx, req.UserKSUID, req.ContentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("内部获取播放历史记录失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 构建响应
	response := &dto.InternalGetPlayHistoryResponse{
		Found: playHistory != nil,
	}

	if playHistory != nil {
		response.PlayHistoryItemID = playHistory.PlayHistoryItemID
		response.UserKSUID = playHistory.UserKSUID
		response.ContentKSUID = playHistory.ContentKSUID
		response.ContentType = string(playHistory.ContentType)
		response.PlayDuration = playHistory.PlayDuration
		response.CreatedAt = playHistory.CreatedAt
		response.UpdatedAt = playHistory.UpdatedAt
	}

	log.Debug().
		Str("user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Bool("found", response.Found).
		Msg("内部获取播放历史记录成功")

	return response, nil
}

// CheckPlayHistoryExists 检查播放历史是否存在
func (s *InternalPlayHistoryServiceImpl) CheckPlayHistoryExists(ctx context.Context, userKSUID, contentKSUID string) (bool, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Msg("开始检查播放历史是否存在")

	exists, err := s.playHistoryRepo.ExistsByUserAndContent(ctx, userKSUID, contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Msg("检查播放历史是否存在失败")
		return false, err
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", contentKSUID).
		Bool("exists", exists).
		Msg("检查播放历史是否存在成功")

	return exists, nil
}

// GetUserPlayHistoryStats 获取用户播放历史统计信息
func (s *InternalPlayHistoryServiceImpl) GetUserPlayHistoryStats(ctx context.Context, userKSUID string) (map[string]int64, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Msg("开始获取用户播放历史统计信息")

	stats, err := s.playHistoryRepo.GetUserPlayHistoryStats(ctx, userKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取用户播放历史统计信息失败")
		return nil, err
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Interface("stats", stats).
		Msg("获取用户播放历史统计信息成功")

	return stats, nil
}

// GetRecentPlayHistories 获取用户最近播放的内容
func (s *InternalPlayHistoryServiceImpl) GetRecentPlayHistories(ctx context.Context, userKSUID string, limit int) ([]dto.PlayHistoryItemDTO, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("limit", limit).
		Msg("开始获取用户最近播放的内容")

	playHistories, err := s.playHistoryRepo.GetRecentPlayHistories(ctx, userKSUID, limit)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("limit", limit).
			Msg("获取用户最近播放的内容失败")
		return nil, err
	}

	// 转换为DTO
	playHistoryItems := make([]dto.PlayHistoryItemDTO, len(playHistories))
	for i, playHistory := range playHistories {
		playHistoryItems[i] = dto.PlayHistoryItemDTO{
			PlayHistoryItemID: playHistory.PlayHistoryItemID,
			ContentKSUID:      playHistory.ContentKSUID,
			ContentType:       string(playHistory.ContentType),
			PlayDuration:      playHistory.PlayDuration,
			CreatedAt:         playHistory.CreatedAt,
			UpdatedAt:         playHistory.UpdatedAt,
		}
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("limit", limit).
		Int("count", len(playHistoryItems)).
		Msg("获取用户最近播放的内容成功")

	return playHistoryItems, nil
}
