package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/intra/service"
	"pxpat-backend/pkg/errors"
	globalTypes "pxpat-backend/pkg/types"
)

type InternalLikeHandler struct {
	internalLikeService service.InternalLikeService
}

func NewInternalLikeHandler(internalLikeService service.InternalLikeService) *InternalLikeHandler {
	return &InternalLikeHandler{
		internalLikeService: internalLikeService,
	}
}

// CheckUserLikeStatus 检查用户对内容的点赞状态
func (h *InternalLikeHandler) CheckUserLikeStatus(c *gin.Context) {
	log.Info().
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到检查用户点赞状态请求")

	// 解析查询参数
	var req dto.CheckUserLikeStatusRequest
	if err := c.ShouldBind<PERSON>uery(&req); err != nil {
		log.Error().
			Err(err).
			Msg("解析检查用户点赞状态请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	likeType, exists, err := h.internalLikeService.CheckUserLikeStatus(req.UserKSUID, req.ContentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("检查用户点赞状态失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: errors.SYSTEM_ERROR,
		})
		return
	}

	response := dto.CheckUserLikeStatusResponse{
		LikeType: likeType,
		Exists:   exists,
	}

	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("like_type", likeType).
		Bool("exists", exists).
		Msg("检查用户点赞状态成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// GetContentLikeCounts 获取内容的点赞统计数据
func (h *InternalLikeHandler) GetContentLikeCounts(c *gin.Context) {
	log.Info().
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到获取内容点赞统计请求")

	// 解析查询参数
	var req dto.GetContentLikeCountsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Err(err).
			Msg("解析获取内容点赞统计请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	likeCount, dislikeCount, err := h.internalLikeService.GetContentLikeCounts(req.ContentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", req.ContentKSUID).
			Msg("获取内容点赞统计失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: errors.SYSTEM_ERROR,
		})
		return
	}

	response := dto.GetContentLikeCountsResponse{
		LikeCount:    likeCount,
		DislikeCount: dislikeCount,
	}

	log.Info().
		Str("content_ksuid", req.ContentKSUID).
		Int64("like_count", likeCount).
		Int64("dislike_count", dislikeCount).
		Msg("获取内容点赞统计成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// BatchGetContentLikeStats 批量获取内容点赞统计
func (h *InternalLikeHandler) BatchGetContentLikeStats(c *gin.Context) {
	log.Info().
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到批量获取内容点赞统计请求")

	// 解析请求参数
	var req dto.BatchGetContentLikeStatsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Err(err).
			Msg("解析批量获取内容点赞统计请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	stats, err := h.internalLikeService.BatchGetContentLikeStats(req.ContentKSUIDs)
	if err != nil {
		log.Error().
			Err(err).
			Int("content_count", len(req.ContentKSUIDs)).
			Msg("批量获取内容点赞统计失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: errors.SYSTEM_ERROR,
		})
		return
	}

	response := dto.BatchGetContentLikeStatsResponse{
		Stats: stats,
	}

	log.Info().
		Int("content_count", len(req.ContentKSUIDs)).
		Int("result_count", len(stats)).
		Msg("批量获取内容点赞统计成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

// BatchCheckUserLikeStatus 批量检查用户点赞状态
func (h *InternalLikeHandler) BatchCheckUserLikeStatus(c *gin.Context) {
	log.Info().
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到批量检查用户点赞状态请求")

	// 解析请求参数
	var req dto.BatchCheckUserLikeStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Err(err).
			Msg("解析批量检查用户点赞状态请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	statuses, err := h.internalLikeService.BatchCheckUserLikeStatus(req.UserKSUID, req.ContentKSUIDs)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", req.UserKSUID).
			Int("content_count", len(req.ContentKSUIDs)).
			Msg("批量检查用户点赞状态失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: errors.SYSTEM_ERROR,
		})
		return
	}

	response := dto.BatchCheckUserLikeStatusResponse{
		Statuses: statuses,
	}

	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Int("content_count", len(req.ContentKSUIDs)).
		Int("result_count", len(statuses)).
		Msg("批量检查用户点赞状态成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}
