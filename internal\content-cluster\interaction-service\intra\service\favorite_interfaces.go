package service

import (
	"context"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
)

// InternalFavoriteService 内部收藏服务接口（供其他微服务调用）
type InternalFavoriteService interface {
	// AddToFavoriteInternal 内部添加到收藏夹
	AddToFavoriteInternal(ctx context.Context, userKSUID string, contentKSUID string, contentType model.ContentType, folderID string) error
	// RemoveFromFavoriteInternal 内部从收藏夹移除
	RemoveFromFavoriteInternal(ctx context.Context, userKSUID string, contentKSUID string, folderID string) error
	// CheckFavoriteStatusInternal 内部检查收藏状态
	CheckFavoriteStatusInternal(ctx context.Context, userKSUID string, contentKSUID string) (bool, []string, error)
	// GetUserFavoriteStatsInternal 内部获取用户收藏统计
	GetUserFavoriteStatsInternal(ctx context.Context, userKSUID string) (*dto.GetFavoriteStatsResponse, error)
	// BatchCheckFavoriteStatusInternal 内部批量检查收藏状态
	BatchCheckFavoriteStatusInternal(ctx context.Context, userKSUID string, contentKSUIDs []string) (map[string]bool, error)
}
