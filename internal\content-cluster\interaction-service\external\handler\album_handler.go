package handler

import (
	"net/http"
	dto2 "pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/external/service"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	"pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"
	globalTypes "pxpat-backend/pkg/types"
)

type AlbumHandler struct {
	albumService *service.AlbumService
}

func NewAlbumHandler(albumService *service.AlbumService) *AlbumHandler {
	return &AlbumHandler{
		albumService: albumService,
	}
}

// CreateAlbum 创建合集
func (h *AlbumHandler) CreateAlbum(c *gin.Context) {
	// 创建子span
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "CreateAlbum")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	var req dto2.CreateAlbumRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Msg("创建合集请求参数绑定失败")

		opentelemetry.AddError(span, &errors.Errors{Err: err})
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMS,
		})
		return
	}

	// 从JWT中获取用户KSUID
	req.UserKSUID = ksuid.GetKSUID(c)

	// 添加span属性
	opentelemetry.AddAttribute(span, "user_ksuid", req.UserKSUID)
	opentelemetry.AddAttribute(span, "album_name", req.AlbumName)
	opentelemetry.AddAttribute(span, "album_type", req.AlbumType)
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "client_ip", c.ClientIP())

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", req.UserKSUID).
		Str("album_name", req.AlbumName).
		Str("album_type", req.AlbumType).
		Msg("收到创建合集请求")

	opentelemetry.AddEvent(span, "service_call_start")

	// 调用服务层
	result, gErr := h.albumService.CreateAlbum(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(gErr.Err).
			Int("internal_error_code", gErr.InternalCode).
			Str("user_ksuid", req.UserKSUID).
			Msg("创建合集失败")

		opentelemetry.AddError(span, gErr)
		opentelemetry.AddEvent(span, "service_call_failed")

		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", req.UserKSUID).
		Str("album_ksuid", result.AlbumKSUID).
		Msg("创建合集成功")

	opentelemetry.AddEvent(span, "service_call_success")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// GetAlbum 获取合集信息
func (h *AlbumHandler) GetAlbum(c *gin.Context) {
	// 创建子span
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetAlbum")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	albumKSUID := c.Param("album_ksuid")
	if albumKSUID == "" {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Msg("合集KSUID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMS,
		})
		return
	}

	req := dto2.GetAlbumRequest{
		UserKSUID:  ksuid.GetKSUID(c), // 可能为空，用于权限检查
		AlbumKSUID: albumKSUID,
	}

	// 添加span属性
	opentelemetry.AddAttribute(span, "user_ksuid", req.UserKSUID)
	opentelemetry.AddAttribute(span, "album_ksuid", req.AlbumKSUID)

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", req.UserKSUID).
		Str("album_ksuid", req.AlbumKSUID).
		Msg("收到获取合集请求")

	opentelemetry.AddEvent(span, "service_call_start")

	// 调用服务层
	result, gErr := h.albumService.GetAlbum(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(gErr.Err).
			Int("internal_error_code", gErr.InternalCode).
			Str("album_ksuid", req.AlbumKSUID).
			Msg("获取合集失败")

		opentelemetry.AddError(span, gErr)
		opentelemetry.AddEvent(span, "service_call_failed")

		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.ALBUM_NOT_FOUND {
			statusCode = http.StatusNotFound
		} else if gErr.Code == errors.PERMISSION_DENIED {
			statusCode = http.StatusForbidden
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("album_ksuid", req.AlbumKSUID).
		Msg("获取合集成功")

	opentelemetry.AddEvent(span, "service_call_success")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// UpdateAlbum 更新合集信息
func (h *AlbumHandler) UpdateAlbum(c *gin.Context) {
	// 创建子span
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "UpdateAlbum")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	albumKSUID := c.Param("album_ksuid")
	if albumKSUID == "" {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Msg("合集KSUID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMS,
		})
		return
	}

	var req dto2.UpdateAlbumRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Msg("更新合集请求参数绑定失败")

		opentelemetry.AddError(span, &errors.Errors{Err: err})
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMS,
		})
		return
	}

	// 从JWT和路径参数中获取信息
	req.UserKSUID = ksuid.GetKSUID(c)
	req.AlbumKSUID = albumKSUID

	// 添加span属性
	opentelemetry.AddAttribute(span, "user_ksuid", req.UserKSUID)
	opentelemetry.AddAttribute(span, "album_ksuid", req.AlbumKSUID)
	opentelemetry.AddAttribute(span, "album_name", req.AlbumName)

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", req.UserKSUID).
		Str("album_ksuid", req.AlbumKSUID).
		Str("album_name", req.AlbumName).
		Msg("收到更新合集请求")

	opentelemetry.AddEvent(span, "service_call_start")

	// 调用服务层
	result, gErr := h.albumService.UpdateAlbum(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(gErr.Err).
			Int("internal_error_code", gErr.InternalCode).
			Str("album_ksuid", req.AlbumKSUID).
			Msg("更新合集失败")

		opentelemetry.AddError(span, gErr)
		opentelemetry.AddEvent(span, "service_call_failed")

		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.ALBUM_NOT_FOUND {
			statusCode = http.StatusNotFound
		} else if gErr.Code == errors.PERMISSION_DENIED {
			statusCode = http.StatusForbidden
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("album_ksuid", req.AlbumKSUID).
		Msg("更新合集成功")

	opentelemetry.AddEvent(span, "service_call_success")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// DeleteAlbum 删除合集
func (h *AlbumHandler) DeleteAlbum(c *gin.Context) {
	// 创建子span
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "DeleteAlbum")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	albumKSUID := c.Param("album_ksuid")
	if albumKSUID == "" {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Msg("合集KSUID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMS,
		})
		return
	}

	req := dto2.DeleteAlbumRequest{
		UserKSUID:  ksuid.GetKSUID(c),
		AlbumKSUID: albumKSUID,
	}

	// 添加span属性
	opentelemetry.AddAttribute(span, "user_ksuid", req.UserKSUID)
	opentelemetry.AddAttribute(span, "album_ksuid", req.AlbumKSUID)

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", req.UserKSUID).
		Str("album_ksuid", req.AlbumKSUID).
		Msg("收到删除合集请求")

	opentelemetry.AddEvent(span, "service_call_start")

	// 调用服务层
	gErr := h.albumService.DeleteAlbum(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(gErr.Err).
			Int("internal_error_code", gErr.InternalCode).
			Str("album_ksuid", req.AlbumKSUID).
			Msg("删除合集失败")

		opentelemetry.AddError(span, gErr)
		opentelemetry.AddEvent(span, "service_call_failed")

		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.ALBUM_NOT_FOUND {
			statusCode = http.StatusNotFound
		} else if gErr.Code == errors.PERMISSION_DENIED {
			statusCode = http.StatusForbidden
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("album_ksuid", req.AlbumKSUID).
		Msg("删除合集成功")

	opentelemetry.AddEvent(span, "service_call_success")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
	})
}

// GetUserAlbums 获取用户合集列表
func (h *AlbumHandler) GetUserAlbums(c *gin.Context) {
	// 创建子span
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetUserAlbums")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Msg("用户KSUID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMS,
		})
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	albumType := c.Query("album_type")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	req := dto2.GetUserAlbumsRequest{
		RequestUserKSUID: ksuid.GetKSUID(c), // 请求者KSUID
		UserKSUID:        userKSUID,         // 目标用户KSUID
		Page:             page,
		PageSize:         pageSize,
		AlbumType:        albumType,
	}

	// 添加span属性
	opentelemetry.AddAttribute(span, "request_user_ksuid", req.RequestUserKSUID)
	opentelemetry.AddAttribute(span, "target_user_ksuid", req.UserKSUID)
	opentelemetry.AddAttribute(span, "page", page)
	opentelemetry.AddAttribute(span, "page_size", pageSize)

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("request_user_ksuid", req.RequestUserKSUID).
		Str("target_user_ksuid", req.UserKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Str("album_type", albumType).
		Msg("收到获取用户合集列表请求")

	opentelemetry.AddEvent(span, "service_call_start")

	// 调用服务层
	result, gErr := h.albumService.GetUserAlbums(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(gErr.Err).
			Int("internal_error_code", gErr.InternalCode).
			Str("target_user_ksuid", req.UserKSUID).
			Msg("获取用户合集列表失败")

		opentelemetry.AddError(span, gErr)
		opentelemetry.AddEvent(span, "service_call_failed")

		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.USER_NOT_FOUND {
			statusCode = http.StatusNotFound
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("target_user_ksuid", req.UserKSUID).
		Int64("total", result.Total).
		Msg("获取用户合集列表成功")

	opentelemetry.AddEvent(span, "service_call_success")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// BatchAddContents 批量添加内容到合集
func (h *AlbumHandler) BatchAddContents(c *gin.Context) {
	// 创建子span
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchAddContents")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	var req dto2.BatchAddContentsToAlbumRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Msg("批量添加内容请求参数绑定失败")

		opentelemetry.AddError(span, &errors.Errors{Err: err})
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMS,
		})
		return
	}

	// 从JWT中获取用户KSUID
	req.UserKSUID = ksuid.GetKSUID(c)

	// 添加span属性
	opentelemetry.AddAttribute(span, "user_ksuid", req.UserKSUID)
	opentelemetry.AddAttribute(span, "album_ksuid", req.AlbumKSUID)
	opentelemetry.AddAttribute(span, "content_count", len(req.Contents))

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", req.UserKSUID).
		Str("album_ksuid", req.AlbumKSUID).
		Int("content_count", len(req.Contents)).
		Msg("收到批量添加内容请求")

	opentelemetry.AddEvent(span, "service_call_start")

	// 调用服务层
	result, gErr := h.albumService.BatchAddContents(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(gErr.Err).
			Int("internal_error_code", gErr.InternalCode).
			Str("album_ksuid", req.AlbumKSUID).
			Msg("批量添加内容失败")

		opentelemetry.AddError(span, gErr)
		opentelemetry.AddEvent(span, "service_call_failed")

		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.ALBUM_NOT_FOUND {
			statusCode = http.StatusNotFound
		} else if gErr.Code == errors.PERMISSION_DENIED {
			statusCode = http.StatusForbidden
		} else if gErr.Code == errors.CONTENT_TYPE_MISMATCH {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("album_ksuid", req.AlbumKSUID).
		Int("success_count", result.SuccessCount).
		Int("failed_count", result.FailedCount).
		Msg("批量添加内容完成")

	opentelemetry.AddEvent(span, "service_call_success")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// BatchRemoveContents 批量从合集移除内容
func (h *AlbumHandler) BatchRemoveContents(c *gin.Context) {
	// 创建子span
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchRemoveContents")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	var req dto2.BatchRemoveContentsFromAlbumRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Msg("批量移除内容请求参数绑定失败")

		opentelemetry.AddError(span, &errors.Errors{Err: err})
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMS,
		})
		return
	}

	// 从JWT中获取用户KSUID
	req.UserKSUID = ksuid.GetKSUID(c)

	// 添加span属性
	opentelemetry.AddAttribute(span, "user_ksuid", req.UserKSUID)
	opentelemetry.AddAttribute(span, "album_ksuid", req.AlbumKSUID)
	opentelemetry.AddAttribute(span, "content_count", len(req.ContentKSUIDs))

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", req.UserKSUID).
		Str("album_ksuid", req.AlbumKSUID).
		Int("content_count", len(req.ContentKSUIDs)).
		Msg("收到批量移除内容请求")

	opentelemetry.AddEvent(span, "service_call_start")

	// 调用服务层
	result, gErr := h.albumService.BatchRemoveContents(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(gErr.Err).
			Int("internal_error_code", gErr.InternalCode).
			Str("album_ksuid", req.AlbumKSUID).
			Msg("批量移除内容失败")

		opentelemetry.AddError(span, gErr)
		opentelemetry.AddEvent(span, "service_call_failed")

		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.ALBUM_NOT_FOUND {
			statusCode = http.StatusNotFound
		} else if gErr.Code == errors.PERMISSION_DENIED {
			statusCode = http.StatusForbidden
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("album_ksuid", req.AlbumKSUID).
		Int("success_count", result.SuccessCount).
		Int("failed_count", result.FailedCount).
		Msg("批量移除内容完成")

	opentelemetry.AddEvent(span, "service_call_success")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// ==================== 合集内容管理API ====================

// GetAlbumContents 获取合集内容列表
func (h *AlbumHandler) GetAlbumContents(c *gin.Context) {
	// 创建子span
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetAlbumContents")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	albumKSUID := c.Param("album_ksuid")
	if albumKSUID == "" {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Msg("合集KSUID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMS,
		})
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	sortBy := c.Query("sort_by")
	sortOrder := c.Query("sort_order")

	req := dto2.GetAlbumContentsRequest{
		UserKSUID:  ksuid.GetKSUID(c), // 可能为空，用于权限检查
		AlbumKSUID: albumKSUID,
		Page:       page,
		PageSize:   pageSize,
		SortBy:     sortBy,
		SortOrder:  sortOrder,
	}

	// 添加span属性
	opentelemetry.AddAttribute(span, "user_ksuid", req.UserKSUID)
	opentelemetry.AddAttribute(span, "album_ksuid", req.AlbumKSUID)
	opentelemetry.AddAttribute(span, "page", page)
	opentelemetry.AddAttribute(span, "page_size", pageSize)
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "client_ip", c.ClientIP())

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", req.UserKSUID).
		Str("album_ksuid", req.AlbumKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Msg("收到获取合集内容列表请求")

	opentelemetry.AddEvent(span, "service_call_start")

	// 调用服务层
	result, gErr := h.albumService.GetAlbumContents(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(gErr.Err).
			Int("internal_error_code", gErr.InternalCode).
			Str("album_ksuid", req.AlbumKSUID).
			Msg("获取合集内容列表失败")

		opentelemetry.AddError(span, gErr)
		opentelemetry.AddEvent(span, "service_call_failed")

		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.ALBUM_NOT_FOUND {
			statusCode = http.StatusNotFound
		} else if gErr.Code == errors.PERMISSION_DENIED {
			statusCode = http.StatusForbidden
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("album_ksuid", req.AlbumKSUID).
		Int64("total", result.Total).
		Msg("获取合集内容列表成功")

	opentelemetry.AddEvent(span, "service_call_success")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}
