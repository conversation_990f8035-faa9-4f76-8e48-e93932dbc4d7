package service

import (
	"context"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
)

// InternalPlayHistoryService 内部播放历史服务接口
type InternalPlayHistoryService interface {
	// UpdatePlayHistory 内部更新播放历史记录
	UpdatePlayHistory(ctx context.Context, req *dto.InternalUpdatePlayHistoryRequest) (*dto.InternalUpdatePlayHistoryResponse, error)

	// GetPlayHistory 内部获取播放历史记录
	GetPlayHistory(ctx context.Context, req *dto.InternalGetPlayHistoryRequest) (*dto.InternalGetPlayHistoryResponse, error)

	// CheckPlayHistoryExists 检查播放历史是否存在
	CheckPlayHistoryExists(ctx context.Context, userKSUID, contentKSUID string) (bool, error)

	// GetUserPlayHistoryStats 获取用户播放历史统计信息
	GetUserPlayHistoryStats(ctx context.Context, userKSUID string) (map[string]int64, error)

	// GetRecentPlayHistories 获取用户最近播放的内容
	GetRecentPlayHistories(ctx context.Context, userKSUID string, limit int) ([]dto.PlayHistoryItemDTO, error)
}
