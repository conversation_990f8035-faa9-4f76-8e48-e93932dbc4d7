package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/intra/service"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	globalTypes "pxpat-backend/pkg/types"
)

// InternalFavoriteHandler 内部收藏处理器
type InternalFavoriteHandler struct {
	internalFavoriteService *service.InternalFavoriteService
}

// NewInternalFavoriteHandler 创建内部收藏处理器
func NewInternalFavoriteHandler(internalFavoriteService *service.InternalFavoriteService) *InternalFavoriteHandler {
	return &InternalFavoriteHandler{
		internalFavoriteService: internalFavoriteService,
	}
}

// AddToFavoriteInternalRequest 内部添加收藏请求
type AddToFavoriteInternalRequest struct {
	UserKSUID    string `json:"user_ksuid" binding:"required"`
	ContentKSUID string `json:"content_ksuid" binding:"required"`
	ContentType  string `json:"content_type" binding:"required"`
	FolderID     string `json:"folder_id,omitempty"`
}

// RemoveFromFavoriteInternalRequest 内部移除收藏请求
type RemoveFromFavoriteInternalRequest struct {
	UserKSUID    string `json:"user_ksuid" binding:"required"`
	ContentKSUID string `json:"content_ksuid" binding:"required"`
	FolderID     string `json:"folder_id,omitempty"`
}

// CheckFavoriteStatusInternalRequest 内部检查收藏状态请求
type CheckFavoriteStatusInternalRequest struct {
	UserKSUID    string `json:"user_ksuid" binding:"required"`
	ContentKSUID string `json:"content_ksuid" binding:"required"`
}

// BatchCheckFavoriteStatusInternalRequest 内部批量检查收藏状态请求
type BatchCheckFavoriteStatusInternalRequest struct {
	UserKSUID     string   `json:"user_ksuid" binding:"required"`
	ContentKSUIDs []string `json:"content_ksuids" binding:"required"`
}

// AddToFavoriteInternal 内部添加到收藏夹
func (h *InternalFavoriteHandler) AddToFavoriteInternal(c *gin.Context) {
	var req AddToFavoriteInternalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("Invalid request parameters for internal add to favorite")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "请求参数无效",
		})
		return
	}

	// 验证内容类型
	contentType := model.ContentType(req.ContentType)
	if !model.IsValidContentType(contentType) {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "不支持的内容类型",
		})
		return
	}

	err := h.internalFavoriteService.AddToFavoriteInternal(
		c.Request.Context(),
		req.UserKSUID,
		req.ContentKSUID,
		contentType,
		req.FolderID,
	)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Failed to add to favorite internally")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "添加收藏失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "添加收藏成功",
	})
}

// RemoveFromFavoriteInternal 内部从收藏夹移除
func (h *InternalFavoriteHandler) RemoveFromFavoriteInternal(c *gin.Context) {
	var req RemoveFromFavoriteInternalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("Invalid request parameters for internal remove from favorite")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "请求参数无效",
		})
		return
	}

	err := h.internalFavoriteService.RemoveFromFavoriteInternal(
		c.Request.Context(),
		req.UserKSUID,
		req.ContentKSUID,
		req.FolderID,
	)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Failed to remove from favorite internally")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "移除收藏失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "移除收藏成功",
	})
}

// CheckFavoriteStatusInternal 内部检查收藏状态
func (h *InternalFavoriteHandler) CheckFavoriteStatusInternal(c *gin.Context) {
	var req CheckFavoriteStatusInternalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("Invalid request parameters for internal check favorite status")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "请求参数无效",
		})
		return
	}

	isFavorited, folderIDs, err := h.internalFavoriteService.CheckFavoriteStatusInternal(
		c.Request.Context(),
		req.UserKSUID,
		req.ContentKSUID,
	)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Failed to check favorite status internally")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "检查收藏状态失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "检查收藏状态成功",
		Data: map[string]interface{}{
			"is_favorited": isFavorited,
			"folder_ids":   folderIDs,
		},
	})
}

// GetUserFavoriteStatsInternal 内部获取用户收藏统计
func (h *InternalFavoriteHandler) GetUserFavoriteStatsInternal(c *gin.Context) {
	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "用户ID不能为空",
		})
		return
	}

	stats, err := h.internalFavoriteService.GetUserFavoriteStatsInternal(c.Request.Context(), userKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("Failed to get user favorite stats internally")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "获取收藏统计失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "获取收藏统计成功",
		Data:    stats,
	})
}

// BatchCheckFavoriteStatusInternal 内部批量检查收藏状态
func (h *InternalFavoriteHandler) BatchCheckFavoriteStatusInternal(c *gin.Context) {
	var req BatchCheckFavoriteStatusInternalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("Invalid request parameters for internal batch check favorite status")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "请求参数无效",
		})
		return
	}

	favoriteMap, err := h.internalFavoriteService.BatchCheckFavoriteStatusInternal(
		c.Request.Context(),
		req.UserKSUID,
		req.ContentKSUIDs,
	)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", req.UserKSUID).
			Msg("Failed to batch check favorite status internally")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "批量检查收藏状态失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "批量检查收藏状态成功",
		Data:    favoriteMap,
	})
}
