package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/external/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	globalTypes "pxpat-backend/pkg/types"
)

type LikeHandler struct {
	likeService service.LikeService
}

func NewLikeHandler(likeService service.LikeService) *LikeHandler {
	return &LikeHandler{
		likeService: likeService,
	}
}

func (h *LikeHandler) LikeContent(c *gin.Context) {
	// 获取用户ID
	userKSUID := ksuid.GetKSUID(c)

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到点赞/不喜欢内容请求")

	// 解析请求参数
	var req dto.LikeContentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("解析点赞/不喜欢请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.likeService.LikeContent(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Str("type", req.Type).
			Msg("点赞/不喜欢内容失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Str("type", response.Type).
		Bool("is_active", response.IsActive).
		Int64("like_count", response.LikeCount).
		Int64("dislike_count", response.DislikeCount).
		Msg("点赞/不喜欢内容成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

func (h *LikeHandler) GetMyLikes(c *gin.Context) {
	// 获取用户ID
	userKSUID := ksuid.GetKSUID(c)

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到获取用户点赞记录请求")

	// 解析查询参数
	var req dto.GetUserLikesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("解析获取用户点赞记录请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.likeService.GetUserLikes(c.Request.Context(), userKSUID, &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Str("type", req.Type).
			Str("content_type", req.ContentType).
			Msg("获取用户点赞记录失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("type", req.Type).
		Str("content_type", req.ContentType).
		Int64("total", response.Total).
		Int("count", len(response.Likes)).
		Msg("获取用户点赞记录成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}

func (h *LikeHandler) GetOtherUserLikes(c *gin.Context) {
	log.Info().
		Str("method", c.Request.Method).
		Str("path", c.Request.URL.Path).
		Msg("收到获取他人点赞记录请求")

	// 解析查询参数
	var req dto.GetOtherUserLikesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().
			Err(err).
			Msg("解析获取他人点赞记录请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 调用服务层
	response, gErr := h.likeService.GetOtherUserLikes(c.Request.Context(), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("current_user_ksuid", CurrentUserKSUID).
			Str("target_user_ksuid", req.UserKSUID).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Str("type", req.Type).
			Str("content_type", req.ContentType).
			Msg("获取他人点赞记录失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	log.Info().
		Str("current_user_ksuid", CurrentUserKSUID).
		Str("target_user_ksuid", req.UserKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("type", req.Type).
		Str("content_type", req.ContentType).
		Int64("total", response.Total).
		Int("count", len(response.Likes)).
		Msg("获取他人点赞记录成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: response,
	})
}
