package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	externalHandler "pxpat-backend/internal/content-cluster/interaction-service/external/handler"
	intraHandler "pxpat-backend/internal/content-cluster/interaction-service/intra/handler"
	"pxpat-backend/internal/content-cluster/interaction-service/routes/album"
	"pxpat-backend/internal/content-cluster/interaction-service/routes/favorite"
	"pxpat-backend/internal/content-cluster/interaction-service/routes/like"
	"pxpat-backend/internal/content-cluster/interaction-service/routes/play_history"
	"pxpat-backend/pkg/auth"
)

func RegisterRoutes(
	r *gin.Engine,
	jwtManager *auth.Manager,
	albumHandler *externalHandler.AlbumHandler,
	albumContentHandler *externalHandler.AlbumContentHandler,
	favoriteFolderHandler *externalHandler.FavoriteFolderHandler,
	favoriteItemHandler *externalHandler.FavoriteItemHandler,
	internalFavoriteHandler *intraHandler.InternalFavoriteHandler,
	externalLikeHandler *externalHandler.<PERSON><PERSON><PERSON><PERSON>,
	internalLikeHandler *intraHandler.InternalLikeHandler,
	playHistoryHandler *externalHandler.PlayHistoryHandler,
	internalPlayHistoryHandler *intraHandler.InternalPlayHistoryHandler,
) {
	log.Info().Msg("Registering interaction service routes")

	// 创建路由组
	externalAPI := r.Group("/api/v1")
	internalAPI := r.Group("/internal/v1")

	// 注册所有合集相关路由
	album.RegisterAlbumRoutes(r, jwtManager, albumHandler, albumContentHandler)

	// 注册所有收藏相关路由
	favorite.RegisterAllFavoriteRoutes(
		externalAPI,
		internalAPI,
		favoriteFolderHandler,
		favoriteItemHandler,
		internalFavoriteHandler,
		jwtManager,
	)

	// 注册所有点赞相关路由
	like.RegisterAllLikeRoutes(
		externalAPI,
		internalAPI,
		externalLikeHandler,
		internalLikeHandler,
		jwtManager,
	)

	// 注册所有播放历史相关路由
	play_history.RegisterAllPlayHistoryRoutes(
		externalAPI,
		internalAPI,
		playHistoryHandler,
		internalPlayHistoryHandler,
		jwtManager,
	)

	log.Info().Msg("Interaction service routes registered successfully")
}
