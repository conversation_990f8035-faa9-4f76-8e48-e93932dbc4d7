package service

import (
	"context"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
)

// FavoriteFolderService 收藏夹服务接口
type FavoriteFolderService interface {
	// CreateFolder 创建收藏夹
	CreateFolder(ctx context.Context, userKSUID string, req *dto.CreateFavoriteFolderRequest) (*dto.FavoriteFolderResponse, error)

	// UpdateFolder 更新收藏夹
	UpdateFolder(ctx context.Context, userKSUID, folderID string, req *dto.UpdateFavoriteFolderRequest) (*dto.FavoriteFolderResponse, error)

	// DeleteFolder 删除收藏夹
	DeleteFolder(ctx context.Context, userKSUID, folderID string) error

	// GetFolder 获取单个收藏夹详情
	GetFolder(ctx context.Context, currentUserKSUID, folderID string) (*dto.FavoriteFolderResponse, error)

	// GetFolders 获取用户的收藏夹列表
	GetFolders(ctx context.Context, currentUserKSUID string, req *dto.GetFavoriteFoldersRequest) (*dto.GetFavoriteFoldersResponse, error)

	// CreateDefaultFolderForUser 为用户创建默认收藏夹
	CreateDefaultFolderForUser(ctx context.Context, userKSUID string) (*dto.FavoriteFolderResponse, error)

	// GetFavoriteStats 获取收藏统计信息
	GetFavoriteStats(ctx context.Context, userKSUID string) (*dto.GetFavoriteStatsResponse, error)
}

// FavoriteItemService 收藏项服务接口
type FavoriteItemService interface {
	// AddToFavorite 添加到收藏夹
	AddToFavorite(ctx context.Context, userKSUID string, req *dto.AddToFavoriteRequest) error

	// RemoveFromFavorite 从收藏夹移除
	RemoveFromFavorite(ctx context.Context, userKSUID string, req *dto.RemoveFromFavoriteRequest) error

	// MoveFavoriteItem 移动收藏项
	MoveFavoriteItem(ctx context.Context, userKSUID string, req *dto.MoveFavoriteItemRequest) (*dto.BatchFavoriteOperationResponse, error)

	// GetFavoriteItems 获取收藏项列表
	GetFavoriteItems(ctx context.Context, currentUserKSUID string, req *dto.GetFavoriteItemsRequest) (*dto.GetFavoriteItemsResponse, error)

	// CheckFavoriteStatus 检查收藏状态
	CheckFavoriteStatus(ctx context.Context, userKSUID string, req *dto.CheckFavoriteStatusRequest) (*dto.CheckFavoriteStatusResponse, error)
}
