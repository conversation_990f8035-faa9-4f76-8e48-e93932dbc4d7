package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/external/service"
	"pxpat-backend/pkg/ksuid"
	globalTypes "pxpat-backend/pkg/types"
)

// FavoriteItemHandler 收藏项处理器
type FavoriteItemHandler struct {
	favoriteItemService service.FavoriteItemService
}

// NewFavoriteItemHandler 创建收藏项处理器
func NewFavoriteItemHandler(favoriteItemService service.FavoriteItemService) *FavoriteItemHandler {
	return &FavoriteItemHandler{
		favoriteItemService: favoriteItemService,
	}
}

// AddToFavorite 添加到收藏夹
func (h *FavoriteItemHandler) AddToFavorite(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	var req dto.AddToFavoriteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("Invalid request parameters for add to favorite")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "请求参数无效",
		})
		return
	}

	// 参数验证
	if req.ContentKSUID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "内容ID不能为空",
		})
		return
	}

	err := h.favoriteItemService.AddToFavorite(c.Request.Context(), userKSUID, &req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Failed to add to favorite")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "添加收藏失败",
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("Successfully added to favorite")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "添加收藏成功",
	})
}

// RemoveFromFavorite 从收藏夹移除
func (h *FavoriteItemHandler) RemoveFromFavorite(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	var req dto.RemoveFromFavoriteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("Invalid request parameters for remove from favorite")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "请求参数无效",
		})
		return
	}

	// 参数验证
	if req.ContentKSUID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "内容ID不能为空",
		})
		return
	}

	err := h.favoriteItemService.RemoveFromFavorite(c.Request.Context(), userKSUID, &req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Failed to remove from favorite")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "移除收藏失败",
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("Successfully removed from favorite")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "移除收藏成功",
	})
}

// MoveFavoriteItem 移动收藏项
func (h *FavoriteItemHandler) MoveFavoriteItem(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	var req dto.MoveFavoriteItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Msg("Invalid request parameters for move favorite item")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "请求参数无效",
		})
		return
	}

	// 参数验证
	if req.ContentKSUID == "" || req.TargetFolderID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "内容ID和目标收藏夹ID不能为空",
		})
		return
	}

	result, err := h.favoriteItemService.MoveFavoriteItem(c.Request.Context(), userKSUID, &req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("target_folder_id", req.TargetFolderID).
			Msg("Failed to move favorite item")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "移动收藏项失败",
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("target_folder_id", req.TargetFolderID).
		Msg("Successfully moved favorite item")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "移动收藏项成功",
		Data:    result,
	})
}

// GetFavoriteItems 获取收藏项列表
func (h *FavoriteItemHandler) GetFavoriteItems(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	// 解析查询参数
	var req dto.GetFavoriteItemsRequest
	req.FolderID = c.Query("folder_id")
	req.ContentType = c.Query("content_type")
	req.SortBy = c.Query("sort_by")
	req.SortOrder = c.Query("sort_order")

	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}
	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 {
			req.PageSize = pageSize
		}
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	response, err := h.favoriteItemService.GetFavoriteItems(c.Request.Context(), userKSUID, &req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("folder_id", req.FolderID).
			Msg("Failed to get favorite items")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "获取收藏项列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "获取收藏项列表成功",
		Data:    response,
	})
}

// CheckFavoriteStatus 检查收藏状态
func (h *FavoriteItemHandler) CheckFavoriteStatus(c *gin.Context) {
	userKSUID := ksuid.GetKSUID(c)

	// 解析查询参数
	var req dto.CheckFavoriteStatusRequest
	req.ContentKSUID = c.Query("content_ksuid")

	// 参数验证
	if req.ContentKSUID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "内容ID不能为空",
		})
		return
	}

	response, err := h.favoriteItemService.CheckFavoriteStatus(c.Request.Context(), userKSUID, &req)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Failed to check favorite status")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "检查收藏状态失败",
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "检查收藏状态成功",
		Data:    response,
	})
}
