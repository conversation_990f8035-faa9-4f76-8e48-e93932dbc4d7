package album

import (
	"github.com/gin-gonic/gin"
	externalHandler "pxpat-backend/internal/content-cluster/interaction-service/external/handler"
	"pxpat-backend/pkg/auth"
)

func RegisterAlbumRoutes(
	r *gin.Engine,
	jwtManager *auth.Manager,
	albumHandler *externalHandler.AlbumHandler,
	albumContentHandler *externalHandler.AlbumContentHandler,
) {
	// 注册外部API路由（用户接口，需要认证）
	api := r.Group("/api/v1")
	RegisterAlbumExternalRoutes(api, albumHandler, albumContentHandler, jwtManager)
}
