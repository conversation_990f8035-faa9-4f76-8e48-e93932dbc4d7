package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/external/service"
	"pxpat-backend/pkg/ksuid"
	globalTypes "pxpat-backend/pkg/types"
)

type PlayHistoryHandler struct {
	playHistoryService *service.PlayHistoryService
}

func NewPlayHistoryHandler(playHistoryService *service.PlayHistoryService) *PlayHistoryHandler {
	return &PlayHistoryHandler{
		playHistoryService: playHistoryService,
	}
}

// UpdatePlayHistory 更新播放历史记录
// @Summary 更新播放历史记录
// @Description 更新用户的播放历史记录，如果不存在则创建
// @Tags 播放历史
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param request body dto.UpdatePlayHistoryRequest true "更新播放历史记录请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.UpdatePlayHistoryResponse} "更新成功"
// @Failure 400 {object} globalTypes.GlobalResponse "请求参数错误"
// @Failure 401 {object} globalTypes.GlobalResponse "未授权"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器内部错误"
// @Router /api/v1/play-history [post]
func (h *PlayHistoryHandler) UpdatePlayHistory(c *gin.Context) {
	// 获取用户KSUID
	userKSUID := ksuid.GetKSUID(c)

	// 解析请求参数
	var req dto.UpdatePlayHistoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("解析更新播放历史记录请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "请求参数格式错误",
		})
		return
	}

	// 设置用户KSUID
	req.UserKSUID = userKSUID

	// 调用服务层
	result, err := h.playHistoryService.UpdatePlayHistory(c.Request.Context(), &req)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Int64("play_duration", req.PlayDuration).
			Msg("更新播放历史记录失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "更新播放历史记录失败",
		})
		return
	}

	log.Info().
		Str("user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Int64("play_duration", req.PlayDuration).
		Str("play_history_item_id", result.PlayHistoryItemID).
		Bool("is_new", result.IsNew).
		Msg("更新播放历史记录成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "更新播放历史记录成功",
		Data:    result,
	})
}

// GetMyPlayHistory 获取我的播放历史记录
// @Summary 获取我的播放历史记录
// @Description 分页获取当前用户的播放历史记录，支持按内容类型过滤
// @Tags 播放历史
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param page query int false "页码，从1开始" default(1)
// @Param page_size query int false "每页数量，默认20，最大100" default(20)
// @Param content_type query string false "内容类型过滤：video/anime/short，为空则获取所有"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.GetMyPlayHistoryResponse} "获取成功"
// @Failure 400 {object} globalTypes.GlobalResponse "请求参数错误"
// @Failure 401 {object} globalTypes.GlobalResponse "未授权"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器内部错误"
// @Router /api/v1/play-history/my [get]
func (h *PlayHistoryHandler) GetMyPlayHistory(c *gin.Context) {
	// 获取用户KSUID
	userKSUID := ksuid.GetKSUID(c)

	// 解析请求参数
	var req dto.GetMyPlayHistoryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error().Err(err).Msg("解析获取播放历史记录请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "请求参数格式错误",
		})
		return
	}

	// 调用服务层
	result, err := h.playHistoryService.GetMyPlayHistory(c.Request.Context(), userKSUID, &req)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Str("content_type", req.ContentType).
			Msg("获取播放历史记录失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "获取播放历史记录失败",
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Str("content_type", req.ContentType).
		Int64("total", result.Total).
		Int("count", len(result.PlayHistories)).
		Msg("获取播放历史记录成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "获取播放历史记录成功",
		Data:    result,
	})
}

// DeleteMyPlayHistory 删除指定的播放历史记录
// @Summary 删除指定的播放历史记录
// @Description 根据播放历史记录ID数组删除指定的播放历史记录
// @Tags 播放历史
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param request body dto.DeletePlayHistoryRequest true "删除播放历史记录请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.DeletePlayHistoryResponse} "删除成功"
// @Failure 400 {object} globalTypes.GlobalResponse "请求参数错误"
// @Failure 401 {object} globalTypes.GlobalResponse "未授权"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器内部错误"
// @Router /api/v1/play-history [delete]
func (h *PlayHistoryHandler) DeleteMyPlayHistory(c *gin.Context) {
	// 获取用户KSUID
	userKSUID := ksuid.GetKSUID(c)

	// 解析请求参数
	var req dto.DeletePlayHistoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("解析删除播放历史记录请求参数失败")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "请求参数格式错误",
		})
		return
	}

	// 调用服务层
	result, err := h.playHistoryService.DeleteMyPlayHistory(c.Request.Context(), userKSUID, &req)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Interface("play_history_item_ids", req.PlayHistoryItemIDs).
			Msg("删除播放历史记录失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "删除播放历史记录失败",
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Interface("play_history_item_ids", req.PlayHistoryItemIDs).
		Int64("deleted_count", result.DeletedCount).
		Msg("删除播放历史记录成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "删除播放历史记录成功",
		Data:    result,
	})
}

// ClearAllMyPlayHistory 清空我的播放历史记录
// @Summary 清空我的播放历史记录
// @Description 清空当前用户的所有播放历史记录
// @Tags 播放历史
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.DeletePlayHistoryResponse} "清空成功"
// @Failure 401 {object} globalTypes.GlobalResponse "未授权"
// @Failure 500 {object} globalTypes.GlobalResponse "服务器内部错误"
// @Router /api/v1/play-history/all [delete]
func (h *PlayHistoryHandler) ClearAllMyPlayHistory(c *gin.Context) {
	// 获取用户KSUID
	userKSUID := ksuid.GetKSUID(c)

	// 调用服务层
	result, err := h.playHistoryService.ClearAllMyPlayHistory(c.Request.Context(), userKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("清空播放历史记录失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    globalTypes.FAIL,
			Message: "清空播放历史记录失败",
		})
		return
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int64("cleared_count", result.DeletedCount).
		Msg("清空播放历史记录成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    globalTypes.SUCCESS,
		Message: "清空播放历史记录成功",
		Data:    result,
	})
}
