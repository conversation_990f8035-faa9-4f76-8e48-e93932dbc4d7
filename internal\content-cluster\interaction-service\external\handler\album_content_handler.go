package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/external/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	"pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"
	globalTypes "pxpat-backend/pkg/types"
)

type AlbumContentHandler struct {
	albumContentService *service.AlbumContentService
}

func NewAlbumContentHandler(albumContentService *service.AlbumContentService) *AlbumContentHandler {
	return &AlbumContentHandler{
		albumContentService: albumContentService,
	}
}

// AddContentToAlbum 添加内容到合集
func (h *AlbumContentHandler) AddContentToAlbum(c *gin.Context) {
	// 创建子span
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "AddContentToAlbum")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	var req dto.AddContentToAlbumRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Msg("添加内容到合集请求参数绑定失败")

		opentelemetry.AddError(span, &errors.Errors{Err: err})
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    errors.INVALID_PARAMETER,
			Message: "请求参数格式错误",
			Data:    nil,
		})
		return
	}

	// 获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Msg("获取用户KSUID失败")

		c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
			Code:    errors.UNAUTHORIZED,
			Message: "用户未认证",
			Data:    nil,
		})
		return
	}

	req.UserKSUID = userKSUID.(string)

	// 验证KSUID格式
	if !ksuid.IsValidKSUID(req.AlbumKSUID) {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("album_ksuid", req.AlbumKSUID).
			Msg("合集KSUID格式无效")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    errors.INVALID_PARAMETER,
			Message: "合集ID格式无效",
			Data:    nil,
		})
		return
	}

	if !ksuid.IsValidKSUID(req.ContentKSUID) {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("内容KSUID格式无效")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    errors.INVALID_PARAMETER,
			Message: "内容ID格式无效",
			Data:    nil,
		})
		return
	}

	// 调用服务层
	response, err := h.albumContentService.AddContentToAlbum(ctx, &req)
	if err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Str("album_ksuid", req.AlbumKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("添加内容到合集失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    err.Code,
			Message: err.Message,
			Data:    nil,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("album_ksuid", req.AlbumKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("添加内容到合集成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    errors.SUCCESS,
		Message: "添加内容到合集成功",
		Data:    response,
	})
}

// RemoveContentFromAlbum 从合集移除内容
func (h *AlbumContentHandler) RemoveContentFromAlbum(c *gin.Context) {
	// 创建子span
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "RemoveContentFromAlbum")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	var req dto.RemoveContentFromAlbumRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Msg("从合集移除内容请求参数绑定失败")

		opentelemetry.AddError(span, &errors.Errors{Err: err})
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    errors.INVALID_PARAMETER,
			Message: "请求参数格式错误",
			Data:    nil,
		})
		return
	}

	// 获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Msg("获取用户KSUID失败")

		c.JSON(http.StatusUnauthorized, globalTypes.GlobalResponse{
			Code:    errors.UNAUTHORIZED,
			Message: "用户未认证",
			Data:    nil,
		})
		return
	}

	req.UserKSUID = userKSUID.(string)

	// 验证KSUID格式
	if !ksuid.IsValidKSUID(req.AlbumKSUID) {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("album_ksuid", req.AlbumKSUID).
			Msg("合集KSUID格式无效")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    errors.INVALID_PARAMETER,
			Message: "合集ID格式无效",
			Data:    nil,
		})
		return
	}

	if !ksuid.IsValidKSUID(req.ContentKSUID) {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("内容KSUID格式无效")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    errors.INVALID_PARAMETER,
			Message: "内容ID格式无效",
			Data:    nil,
		})
		return
	}

	// 调用服务层
	err := h.albumContentService.RemoveContentFromAlbum(ctx, &req)
	if err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Str("album_ksuid", req.AlbumKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("从合集移除内容失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    err.Code,
			Message: err.Message,
			Data:    nil,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("album_ksuid", req.AlbumKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("从合集移除内容成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    errors.SUCCESS,
		Message: "从合集移除内容成功",
		Data:    nil,
	})
}

// GetAlbumContentsByType 根据类型获取合集内容
func (h *AlbumContentHandler) GetAlbumContentsByType(c *gin.Context) {
	// 创建子span
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetAlbumContentsByType")
	defer span.End()

	// 更新Gin上下文
	c.Request = c.Request.WithContext(ctx)

	// 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 获取路径参数
	albumKSUID := c.Param("album_ksuid")
	if !ksuid.IsValidKSUID(albumKSUID) {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("album_ksuid", albumKSUID).
			Msg("合集KSUID格式无效")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code:    errors.INVALID_PARAMETER,
			Message: "合集ID格式无效",
			Data:    nil,
		})
		return
	}

	// 获取查询参数
	contentType := c.Query("content_type")
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 获取用户KSUID（可选）
	userKSUID := ""
	if userKSUIDValue, exists := c.Get("user_ksuid"); exists {
		userKSUID = userKSUIDValue.(string)
	}

	req := &dto.GetAlbumContentsByTypeRequest{
		AlbumKSUID:  albumKSUID,
		ContentType: contentType,
		Page:        page,
		PageSize:    pageSize,
		UserKSUID:   userKSUID,
	}

	// 调用服务层
	response, serviceErr := h.albumContentService.GetAlbumContentsByType(ctx, req)
	if serviceErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(serviceErr).
			Str("album_ksuid", albumKSUID).
			Str("content_type", contentType).
			Msg("获取合集内容失败")

		opentelemetry.AddError(span, serviceErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code:    serviceErr.Code,
			Message: serviceErr.Message,
			Data:    nil,
		})
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("album_ksuid", albumKSUID).
		Str("content_type", contentType).
		Int("page", page).
		Int("page_size", pageSize).
		Msg("获取合集内容成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code:    errors.SUCCESS,
		Message: "获取合集内容成功",
		Data:    response,
	})
}
