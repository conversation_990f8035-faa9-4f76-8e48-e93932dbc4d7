package types

import "time"

type GlobalAllServicesList struct {
	// 用户组
	UserService serviceConfig `mapstructure:"user_service"`
	// 内容组
	AuditService       serviceConfig `mapstructure:"audit_service"`
	VideoService       serviceConfig `mapstructure:"video_service"`
	NovelService       serviceConfig `mapstructure:"novel_service"`
	MusicService       serviceConfig `mapstructure:"music_service"`
	InteractionService serviceConfig `mapstructure:"interaction_service"`
	// 通知组
	NotifyExternalService serviceConfig `mapstructure:"notify_external_service"`
	// 财政组
	PointsService serviceConfig `mapstructure:"points_service"`
	// 存储组
	ContentStorageService serviceConfig `mapstructure:"content_storage_service"`
	MediaProcessService   serviceConfig `mapstructure:"media_process_service"`
	UserStorageService    serviceConfig `mapstructure:"user_storage_service"`
}

type GlobalAllClusterList struct {
	UserCluster   serviceConfig `mapstructure:"user_cluster"`
	NotifyCluster serviceConfig `mapstructure:"notify_cluster"`
}

type serviceConfig struct {
	Host    string        `mapstructure:"host"`
	Port    int           `mapstructure:"port"`
	Timeout time.Duration `mapstructure:"timeout"`
	Token   string        `mapstructure:"token"`
}
