package service

import "pxpat-backend/internal/content-cluster/interaction-service/dto"

// InternalLikeService 内部点赞服务接口（用于服务间调用）
type InternalLikeService interface {
	// CheckUserLikeStatus 检查用户对内容的点赞状态
	CheckUserLikeStatus(userKSUID, contentKSUID string) (likeType string, exists bool, err error)

	// GetContentLikeCounts 获取内容的点赞统计数据
	GetContentLikeCounts(contentKSUID string) (likeCount, dislikeCount int64, err error)

	// BatchGetContentLikeStats 批量获取内容点赞统计
	BatchGetContentLikeStats(contentKSUIDs []string) (map[string]dto.ContentLikeStatsItem, error)

	// BatchCheckUserLikeStatus 批量检查用户点赞状态
	BatchCheckUserLikeStatus(userKSUID string, contentKSUIDs []string) (map[string]dto.UserLikeStatusItem, error)
}
