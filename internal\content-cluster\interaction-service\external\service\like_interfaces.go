package service

import (
	"context"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/pkg/errors"
)

// LikeService 点赞服务接口
type LikeService interface {
	// LikeContent 点赞/不喜欢或取消操作内容
	LikeContent(ctx context.Context, userKSUID string, req *dto.LikeContentRequest) (*dto.LikeContentResponse, *errors.Errors)

	// GetUserLikes 获取用户点赞记录（支持类型过滤）
	GetUserLikes(ctx context.Context, userKSUID string, req *dto.GetUserLikesRequest) (*dto.GetUserLikesResponse, *errors.Errors)

	// GetOtherUserLikes 获取他人点赞记录
	GetOtherUserLikes(ctx context.Context, req *dto.GetOtherUserLikesRequest) (*dto.GetUserLikesResponse, *errors.Errors)
}
