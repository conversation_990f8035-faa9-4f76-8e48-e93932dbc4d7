package impl

import (
	"context"
	"errors"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
)

// likeStatsRepositoryImpl 点赞统计仓储实现
type likeStatsRepositoryImpl struct {
	db *gorm.DB
}

// NewLikeStatsRepository 创建点赞统计仓储实例
func NewLikeStatsRepository(db *gorm.DB) repository.LikeStatsRepository {
	return &likeStatsRepositoryImpl{db: db}
}

// CreateOrUpdateStats 创建或更新内容点赞统计
func (r *likeStatsRepositoryImpl) CreateOrUpdateStats(ctx context.Context, contentKSUID string, contentType model.ContentType, likeCount, dislikeCount int64) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("content_type", string(contentType)).
		Int64("like_count", likeCount).
		Int64("dislike_count", dislikeCount).
		Msg("开始创建或更新内容点赞统计")

	stats := &model.LikeStats{
		ContentKSUID: contentKSUID,
		ContentType:  contentType,
		LikeCount:    likeCount,
		DislikeCount: dislikeCount,
		TotalCount:   likeCount + dislikeCount,
	}

	err := r.db.WithContext(ctx).
		Where("content_ksuid = ?", contentKSUID).
		Assign(map[string]interface{}{
			"like_count":    likeCount,
			"dislike_count": dislikeCount,
			"total_count":   likeCount + dislikeCount,
		}).
		FirstOrCreate(stats).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Str("content_type", string(contentType)).
			Int64("like_count", likeCount).
			Int64("dislike_count", dislikeCount).
			Msg("创建或更新内容点赞统计失败")
		return err
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("content_type", string(contentType)).
		Int64("like_count", likeCount).
		Int64("dislike_count", dislikeCount).
		Msg("创建或更新内容点赞统计成功")

	return nil
}

// GetStatsByContentKSUID 根据内容ID获取统计信息
func (r *likeStatsRepositoryImpl) GetStatsByContentKSUID(ctx context.Context, contentKSUID string) (*model.LikeStats, error) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始获取内容点赞统计")

	var stats model.LikeStats
	err := r.db.WithContext(ctx).
		Where("content_ksuid = ?", contentKSUID).
		First(&stats).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Debug().
				Str("content_ksuid", contentKSUID).
				Msg("内容点赞统计不存在")
			return nil, nil
		}
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容点赞统计失败")
		return nil, err
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("like_count", stats.LikeCount).
		Int64("dislike_count", stats.DislikeCount).
		Msg("获取内容点赞统计成功")

	return &stats, nil
}

// BatchGetStatsByContentKSUIDs 批量获取内容统计信息
func (r *likeStatsRepositoryImpl) BatchGetStatsByContentKSUIDs(ctx context.Context, contentKSUIDs []string) (map[string]*model.LikeStats, error) {
	if len(contentKSUIDs) == 0 {
		return make(map[string]*model.LikeStats), nil
	}

	log.Debug().
		Int("content_count", len(contentKSUIDs)).
		Msg("开始批量获取内容点赞统计")

	var statsList []model.LikeStats
	err := r.db.WithContext(ctx).
		Where("content_ksuid IN ?", contentKSUIDs).
		Find(&statsList).Error

	if err != nil {
		log.Error().
			Err(err).
			Int("content_count", len(contentKSUIDs)).
			Msg("批量获取内容点赞统计失败")
		return nil, err
	}

	// 转换为map
	result := make(map[string]*model.LikeStats)
	for i := range statsList {
		result[statsList[i].ContentKSUID] = &statsList[i]
	}

	log.Debug().
		Int("content_count", len(contentKSUIDs)).
		Int("found_count", len(result)).
		Msg("批量获取内容点赞统计成功")

	return result, nil
}

// IncrementLikeCount 增加点赞数量
func (r *likeStatsRepositoryImpl) IncrementLikeCount(ctx context.Context, contentKSUID string, contentType model.ContentType) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("content_type", string(contentType)).
		Msg("开始增加点赞数量")

	// 使用原子操作更新
	result := r.db.WithContext(ctx).
		Model(&model.LikeStats{}).
		Where("content_ksuid = ?", contentKSUID).
		Updates(map[string]interface{}{
			"like_count":  gorm.Expr("like_count + 1"),
			"total_count": gorm.Expr("(like_count + 1) + dislike_count"),
		})

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", contentKSUID).
			Msg("增加点赞数量失败")
		return result.Error
	}

	// 如果没有找到记录，创建新记录
	if result.RowsAffected == 0 {
		stats := model.NewLikeStats(contentKSUID, contentType)
		stats.IncrementLike()
		err := r.db.WithContext(ctx).Create(stats).Error
		if err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Msg("创建新的点赞统计记录失败")
			return err
		}
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("增加点赞数量成功")

	return nil
}

// DecrementLikeCount 减少点赞数量
func (r *likeStatsRepositoryImpl) DecrementLikeCount(ctx context.Context, contentKSUID string) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始减少点赞数量")

	result := r.db.WithContext(ctx).
		Model(&model.LikeStats{}).
		Where("content_ksuid = ? AND like_count > 0", contentKSUID).
		Updates(map[string]interface{}{
			"like_count":  gorm.Expr("like_count - 1"),
			"total_count": gorm.Expr("(like_count - 1) + dislike_count"),
		})

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", contentKSUID).
			Msg("减少点赞数量失败")
		return result.Error
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("affected_rows", result.RowsAffected).
		Msg("减少点赞数量成功")

	return nil
}

// IncrementDislikeCount 增加点踩数量
func (r *likeStatsRepositoryImpl) IncrementDislikeCount(ctx context.Context, contentKSUID string, contentType model.ContentType) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("content_type", string(contentType)).
		Msg("开始增加点踩数量")

	result := r.db.WithContext(ctx).
		Model(&model.LikeStats{}).
		Where("content_ksuid = ?", contentKSUID).
		Updates(map[string]interface{}{
			"dislike_count": gorm.Expr("dislike_count + 1"),
			"total_count":   gorm.Expr("like_count + (dislike_count + 1)"),
		})

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", contentKSUID).
			Msg("增加点踩数量失败")
		return result.Error
	}

	// 如果没有找到记录，创建新记录
	if result.RowsAffected == 0 {
		stats := model.NewLikeStats(contentKSUID, contentType)
		stats.IncrementDislike()
		err := r.db.WithContext(ctx).Create(stats).Error
		if err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Msg("创建新的点赞统计记录失败")
			return err
		}
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("增加点踩数量成功")

	return nil
}

// DecrementDislikeCount 减少点踩数量
func (r *likeStatsRepositoryImpl) DecrementDislikeCount(ctx context.Context, contentKSUID string) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始减少点踩数量")

	result := r.db.WithContext(ctx).
		Model(&model.LikeStats{}).
		Where("content_ksuid = ? AND dislike_count > 0", contentKSUID).
		Updates(map[string]interface{}{
			"dislike_count": gorm.Expr("dislike_count - 1"),
			"total_count":   gorm.Expr("like_count + (dislike_count - 1)"),
		})

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", contentKSUID).
			Msg("减少点踩数量失败")
		return result.Error
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("affected_rows", result.RowsAffected).
		Msg("减少点踩数量成功")

	return nil
}

// GetTopLikedContent 获取最受欢迎的内容（按点赞数排序）
func (r *likeStatsRepositoryImpl) GetTopLikedContent(ctx context.Context, contentType model.ContentType, limit int) ([]*model.LikeStats, error) {
	log.Debug().
		Str("content_type", string(contentType)).
		Int("limit", limit).
		Msg("开始获取最受欢迎的内容")

	var statsList []model.LikeStats
	query := r.db.WithContext(ctx).
		Where("like_count > 0").
		Order("like_count DESC, total_count DESC").
		Limit(limit)

	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}

	err := query.Find(&statsList).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", string(contentType)).
			Int("limit", limit).
			Msg("获取最受欢迎的内容失败")
		return nil, err
	}

	// 转换为指针切片
	result := make([]*model.LikeStats, len(statsList))
	for i := range statsList {
		result[i] = &statsList[i]
	}

	log.Debug().
		Str("content_type", string(contentType)).
		Int("limit", limit).
		Int("result_count", len(result)).
		Msg("获取最受欢迎的内容成功")

	return result, nil
}

// GetTopDislikedContent 获取最不受欢迎的内容（按点踩数排序）
func (r *likeStatsRepositoryImpl) GetTopDislikedContent(ctx context.Context, contentType model.ContentType, limit int) ([]*model.LikeStats, error) {
	log.Debug().
		Str("content_type", string(contentType)).
		Int("limit", limit).
		Msg("开始获取最不受欢迎的内容")

	var statsList []model.LikeStats
	query := r.db.WithContext(ctx).
		Where("dislike_count > 0").
		Order("dislike_count DESC, total_count DESC").
		Limit(limit)

	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}

	err := query.Find(&statsList).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", string(contentType)).
			Int("limit", limit).
			Msg("获取最不受欢迎的内容失败")
		return nil, err
	}

	// 转换为指针切片
	result := make([]*model.LikeStats, len(statsList))
	for i := range statsList {
		result[i] = &statsList[i]
	}

	log.Debug().
		Str("content_type", string(contentType)).
		Int("limit", limit).
		Int("result_count", len(result)).
		Msg("获取最不受欢迎的内容成功")

	return result, nil
}

// GetControversialContent 获取有争议的内容
func (r *likeStatsRepositoryImpl) GetControversialContent(ctx context.Context, contentType model.ContentType, limit int) ([]*model.LikeStats, error) {
	log.Debug().
		Str("content_type", string(contentType)).
		Int("limit", limit).
		Msg("开始获取有争议的内容")

	var statsList []model.LikeStats
	query := r.db.WithContext(ctx).
		Where("total_count >= 20").
		Where("like_count * 1.0 / total_count BETWEEN 0.3 AND 0.7").
		Order("total_count DESC").
		Limit(limit)

	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}

	err := query.Find(&statsList).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", string(contentType)).
			Int("limit", limit).
			Msg("获取有争议的内容失败")
		return nil, err
	}

	// 转换为指针切片
	result := make([]*model.LikeStats, len(statsList))
	for i := range statsList {
		result[i] = &statsList[i]
	}

	log.Debug().
		Str("content_type", string(contentType)).
		Int("limit", limit).
		Int("result_count", len(result)).
		Msg("获取有争议的内容成功")

	return result, nil
}

// GetStatsByContentType 按内容类型获取统计信息
func (r *likeStatsRepositoryImpl) GetStatsByContentType(ctx context.Context, contentType model.ContentType, page, pageSize int) ([]*model.LikeStats, int64, error) {
	log.Debug().
		Str("content_type", string(contentType)).
		Int("page", page).
		Int("page_size", pageSize).
		Msg("开始按内容类型获取统计信息")

	var statsList []model.LikeStats
	var total int64

	// 构建查询
	query := r.db.WithContext(ctx).Model(&model.LikeStats{})
	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", string(contentType)).
			Msg("获取统计信息总数失败")
		return nil, 0, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 获取分页数据
	err = query.
		Order("total_count DESC, like_count DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&statsList).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", string(contentType)).
			Int("page", page).
			Int("page_size", pageSize).
			Msg("按内容类型获取统计信息失败")
		return nil, 0, err
	}

	// 转换为指针切片
	result := make([]*model.LikeStats, len(statsList))
	for i := range statsList {
		result[i] = &statsList[i]
	}

	log.Debug().
		Str("content_type", string(contentType)).
		Int("page", page).
		Int("page_size", pageSize).
		Int64("total", total).
		Int("result_count", len(result)).
		Msg("按内容类型获取统计信息成功")

	return result, total, nil
}

// RefreshStats 刷新指定内容的统计数据（从分表重新计算）
func (r *likeStatsRepositoryImpl) RefreshStats(ctx context.Context, contentKSUID string) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始刷新内容统计数据")

	// 从所有分表中统计数据
	var likeCount, dislikeCount int64
	tableNames := model.GetAllLikeItemTableNames()

	for _, tableName := range tableNames {
		var counts []struct {
			Type  string `gorm:"column:type"`
			Count int64  `gorm:"column:count"`
		}

		err := r.db.WithContext(ctx).
			Table(tableName).
			Select("type, COUNT(*) as count").
			Where("content_ksuid = ?", contentKSUID).
			Group("type").
			Find(&counts).Error

		if err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Str("table_name", tableName).
				Msg("从分表统计数据失败")
			return err
		}

		for _, count := range counts {
			if count.Type == string(model.LikeTypeLike) {
				likeCount += count.Count
			} else if count.Type == string(model.LikeTypeDislike) {
				dislikeCount += count.Count
			}
		}
	}

	// 更新统计表
	result := r.db.WithContext(ctx).
		Model(&model.LikeStats{}).
		Where("content_ksuid = ?", contentKSUID).
		Updates(map[string]interface{}{
			"like_count":    likeCount,
			"dislike_count": dislikeCount,
			"total_count":   likeCount + dislikeCount,
		})

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", contentKSUID).
			Int64("like_count", likeCount).
			Int64("dislike_count", dislikeCount).
			Msg("更新统计数据失败")
		return result.Error
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("like_count", likeCount).
		Int64("dislike_count", dislikeCount).
		Int64("affected_rows", result.RowsAffected).
		Msg("刷新内容统计数据成功")

	return nil
}

// BatchRefreshStats 批量刷新统计数据
func (r *likeStatsRepositoryImpl) BatchRefreshStats(ctx context.Context, contentKSUIDs []string) error {
	if len(contentKSUIDs) == 0 {
		return nil
	}

	log.Debug().
		Int("content_count", len(contentKSUIDs)).
		Msg("开始批量刷新统计数据")

	for _, contentKSUID := range contentKSUIDs {
		err := r.RefreshStats(ctx, contentKSUID)
		if err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Msg("刷新单个内容统计数据失败")
			return err
		}
	}

	log.Debug().
		Int("content_count", len(contentKSUIDs)).
		Msg("批量刷新统计数据成功")

	return nil
}

// DeleteStats 删除统计记录
func (r *likeStatsRepositoryImpl) DeleteStats(ctx context.Context, contentKSUID string) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始删除统计记录")

	result := r.db.WithContext(ctx).
		Where("content_ksuid = ?", contentKSUID).
		Delete(&model.LikeStats{})

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", contentKSUID).
			Msg("删除统计记录失败")
		return result.Error
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("affected_rows", result.RowsAffected).
		Msg("删除统计记录成功")

	return nil
}
