package service

import (
	"context"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
)

// PlayHistoryService 播放历史服务接口
type PlayHistoryService interface {
	// UpdatePlayHistory 更新播放历史记录
	UpdatePlayHistory(ctx context.Context, req *dto.UpdatePlayHistoryRequest) (*dto.UpdatePlayHistoryResponse, error)

	// GetMyPlayHistory 获取我的播放历史记录
	GetMyPlayHistory(ctx context.Context, userKSUID string, req *dto.GetMyPlayHistoryRequest) (*dto.GetMyPlayHistoryResponse, error)

	// DeleteMyPlayHistory 删除指定的播放历史记录
	DeleteMyPlayHistory(ctx context.Context, userKSUID string, req *dto.DeletePlayHistoryRequest) (*dto.DeletePlayHistoryResponse, error)

	// ClearAllMyPlayHistory 清空我的播放历史记录
	ClearAllMyPlayHistory(ctx context.Context, userKSUID string) (*dto.DeletePlayHistoryResponse, error)
}
