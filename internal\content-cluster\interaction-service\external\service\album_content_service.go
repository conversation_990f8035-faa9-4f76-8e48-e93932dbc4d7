package service

import (
	"context"
	"fmt"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/interaction-service/client"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	"pxpat-backend/pkg/errors"
)

type AlbumContentService struct {
	albumRepository        repository.AlbumRepository
	albumContentRepository repository.AlbumContentRepository
	videoServiceClient     client.VideoServiceClient
	novelServiceClient     client.NovelServiceClient
	musicServiceClient     client.MusicServiceClient
}

func NewAlbumContentService(
	albumRepository repository.AlbumRepository,
	albumContentRepository repository.AlbumContentRepository,
	videoServiceClient client.VideoServiceClient,
	novelServiceClient client.NovelServiceClient,
	musicServiceClient client.MusicServiceClient,
) *AlbumContentService {
	return &AlbumContentService{
		albumRepository:        albumRepository,
		albumContentRepository: albumContentRepository,
		videoServiceClient:     videoServiceClient,
		novelServiceClient:     novelServiceClient,
		musicServiceClient:     musicServiceClient,
	}
}

// AddContentToAlbum 添加内容到合集
func (s *AlbumContentService) AddContentToAlbum(ctx context.Context, req *dto.AddContentToAlbumRequest) (*dto.AddContentToAlbumResponse, *errors.Errors) {
	// 获取合集信息
	album, err := s.albumRepository.GetAlbumByKSUID(ctx, req.AlbumKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(errors.ALBUM_NOT_FOUND, "合集不存在")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取合集信息失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取合集信息失败")
	}

	// 权限检查：只有创建者可以添加内容
	if album.UserKSUID != req.UserKSUID {
		return nil, errors.New(errors.PERMISSION_DENIED, "无权限修改此合集")
	}

	// 验证内容类型
	if album.AlbumType != req.ContentType {
		return nil, errors.New(errors.CONTENT_TYPE_MISMATCH, "内容类型与合集类型不匹配")
	}

	// 根据内容类型检查内容是否存在
	contentInfo, err := s.getContentInfoByType(req.ContentKSUID, req.ContentType)
	if err != nil {
		log.Error().Err(err).Str("content_ksuid", req.ContentKSUID).Str("content_type", req.ContentType).Msg("获取内容信息失败")
		return nil, errors.New(errors.CONTENT_NOT_FOUND, "内容不存在")
	}

	// 验证内容类型
	if contentInfo.ContentType != req.ContentType {
		return nil, errors.New(errors.CONTENT_TYPE_MISMATCH, "内容类型不匹配")
	}

	// 检查内容是否已在合集中
	existing, err := s.albumContentRepository.CheckContentInAlbum(ctx, req.AlbumKSUID, req.ContentKSUID)
	if err == nil && existing != nil {
		return nil, errors.New(errors.CONTENT_ALREADY_IN_ALBUM, "内容已在合集中")
	}

	// 获取排序顺序
	sortOrder := req.SortOrder
	if sortOrder == 0 {
		maxSortOrder, err := s.albumContentRepository.GetMaxSortOrder(ctx, req.AlbumKSUID)
		if err != nil {
			log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取最大排序值失败")
			return nil, errors.New(errors.INTERNAL_ERROR, "获取排序信息失败")
		}
		sortOrder = maxSortOrder + 1
	}

	// 创建合集内容关联记录
	albumContent := &model.AlbumContent{
		AlbumKSUID:   req.AlbumKSUID,
		ContentKSUID: req.ContentKSUID,
		ContentType:  req.ContentType,
		SortOrder:    sortOrder,
	}

	err = s.albumContentRepository.AddContentToAlbum(ctx, albumContent)
	if err != nil {
		log.Error().Err(err).Interface("album_content", albumContent).Msg("添加内容到合集失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "添加内容到合集失败")
	}

	// 更新合集内容数量
	err = s.albumRepository.IncrementAlbumContentCount(ctx, req.AlbumKSUID, 1)
	if err != nil {
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("更新合集内容数量失败")
		// 不返回错误，只记录日志
	}

	log.Info().Str("album_ksuid", req.AlbumKSUID).Str("content_ksuid", req.ContentKSUID).Msg("内容添加到合集成功")

	return &dto.AddContentToAlbumResponse{
		AlbumKSUID:   req.AlbumKSUID,
		ContentKSUID: req.ContentKSUID,
		ContentType:  req.ContentType,
		SortOrder:    sortOrder,
		Success:      true,
	}, nil
}

// RemoveContentFromAlbum 从合集移除内容
func (s *AlbumContentService) RemoveContentFromAlbum(ctx context.Context, req *dto.RemoveContentFromAlbumRequest) *errors.Errors {
	// 获取合集信息
	album, err := s.albumRepository.GetAlbumByKSUID(ctx, req.AlbumKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New(errors.ALBUM_NOT_FOUND, "合集不存在")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取合集信息失败")
		return errors.New(errors.INTERNAL_ERROR, "获取合集信息失败")
	}

	// 权限检查：只有创建者可以移除内容
	if album.UserKSUID != req.UserKSUID {
		return errors.New(errors.PERMISSION_DENIED, "无权限修改此合集")
	}

	// 检查内容是否在合集中
	existing, err := s.albumContentRepository.CheckContentInAlbum(ctx, req.AlbumKSUID, req.ContentKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New(errors.CONTENT_NOT_IN_ALBUM, "内容不在合集中")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Str("content_ksuid", req.ContentKSUID).Msg("检查内容是否在合集中失败")
		return errors.New(errors.INTERNAL_ERROR, "检查内容状态失败")
	}

	if existing == nil {
		return errors.New(errors.CONTENT_NOT_IN_ALBUM, "内容不在合集中")
	}

	// 从合集移除内容
	err = s.albumContentRepository.RemoveContentFromAlbum(ctx, req.AlbumKSUID, req.ContentKSUID)
	if err != nil {
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Str("content_ksuid", req.ContentKSUID).Msg("从合集移除内容失败")
		return errors.New(errors.INTERNAL_ERROR, "从合集移除内容失败")
	}

	// 更新合集内容数量
	err = s.albumRepository.IncrementAlbumContentCount(ctx, req.AlbumKSUID, -1)
	if err != nil {
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("更新合集内容数量失败")
		// 不返回错误，只记录日志
	}

	log.Info().Str("album_ksuid", req.AlbumKSUID).Str("content_ksuid", req.ContentKSUID).Msg("内容从合集移除成功")
	return nil
}

// GetAlbumContentsByType 根据类型获取合集内容
func (s *AlbumContentService) GetAlbumContentsByType(ctx context.Context, req *dto.GetAlbumContentsByTypeRequest) (*dto.GetAlbumContentsResponse, *errors.Errors) {
	// 获取合集信息
	album, err := s.albumRepository.GetAlbumByKSUID(ctx, req.AlbumKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(errors.ALBUM_NOT_FOUND, "合集不存在")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取合集信息失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取合集信息失败")
	}

	// 权限检查：如果是私有合集，只有创建者可以查看
	if !album.IsPublic && req.UserKSUID != album.UserKSUID {
		return nil, errors.New(errors.PERMISSION_DENIED, "无权限查看此合集")
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 计算分页参数
	offset := (req.Page - 1) * req.PageSize

	// 获取合集内容列表
	albumContents, total, err := s.albumContentRepository.GetAlbumContentsByType(ctx, req.AlbumKSUID, req.ContentType, offset, req.PageSize)
	if err != nil {
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Str("content_type", req.ContentType).Msg("获取合集内容列表失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取合集内容列表失败")
	}

	// 转换为响应格式并获取内容详细信息
	contentItems := make([]dto.AlbumContentItem, len(albumContents))
	for i, albumContent := range albumContents {
		contentItem := dto.AlbumContentItem{
			ID:           albumContent.ID,
			AlbumKSUID:   albumContent.AlbumKSUID,
			ContentKSUID: albumContent.ContentKSUID,
			ContentType:  albumContent.ContentType,
			SortOrder:    albumContent.SortOrder,
			AddedAt:      albumContent.CreatedAt,
		}

		// 根据内容类型获取内容详细信息
		contentInfo, err := s.getContentInfoByType(albumContent.ContentKSUID, albumContent.ContentType)
		if err == nil && contentInfo != nil {
			contentItem.Title = contentInfo.Title
			contentItem.Description = contentInfo.Description
			contentItem.CoverURL = contentInfo.CoverURL
			contentItem.Duration = contentInfo.Duration
			contentItem.ViewCount = contentInfo.ViewCount
			contentItem.LikeCount = contentInfo.LikeCount
		}

		contentItems[i] = contentItem
	}

	return &dto.GetAlbumContentsResponse{
		Contents: contentItems,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// ===== 辅助方法 =====

// getContentInfoByType 根据内容类型从相应的服务获取内容信息
func (s *AlbumContentService) getContentInfoByType(contentKSUID, contentType string) (*client.ContentInfo, error) {
	switch contentType {
	case "video", "anime", "short":
		// 视频、动漫、短视频都通过video服务处理
		return s.videoServiceClient.GetContentInfo(contentKSUID)
	case "novel":
		// 小说通过novel服务处理
		return s.novelServiceClient.GetContentInfo(contentKSUID)
	case "music":
		// 音乐通过music服务处理
		return s.musicServiceClient.GetContentInfo(contentKSUID)
	default:
		return nil, fmt.Errorf("不支持的内容类型: %s", contentType)
	}
}
