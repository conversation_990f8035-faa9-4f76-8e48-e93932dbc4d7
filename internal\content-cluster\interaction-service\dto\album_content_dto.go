package dto

import "time"

// GetAlbumContentsRequest 获取合集内容列表请求
type GetAlbumContentsRequest struct {
	UserKSUID  string `json:"-"` // 从JWT获取，用于权限检查
	AlbumKSUID string `json:"-"` // 从路径参数获取
	Page       int    `json:"page" form:"page" binding:"min=1"`
	PageSize   int    `json:"page_size" form:"page_size" binding:"min=1,max=100"`
	SortBy     string `json:"sort_by" form:"sort_by"`       // sort_order, created_at
	SortOrder  string `json:"sort_order" form:"sort_order"` // asc, desc
}

// GetAlbumContentsResponse 获取合集内容列表响应
type GetAlbumContentsResponse struct {
	Contents []AlbumContentItem `json:"contents"`
	Total    int64              `json:"total"`
	Page     int                `json:"page"`
	PageSize int                `json:"page_size"`
}

// AlbumContentItem 合集内容项
type AlbumContentItem struct {
	ID           uint64    `json:"id"`
	AlbumKSUID   string    `json:"album_ksuid"`
	ContentKSUID string    `json:"content_ksuid"`
	ContentType  string    `json:"content_type"`
	SortOrder    int       `json:"sort_order"`
	AddedAt      time.Time `json:"added_at"`

	// 内容详细信息（从视频服务获取）
	Title       string `json:"title,omitempty"`
	Description string `json:"description,omitempty"`
	CoverURL    string `json:"cover_url,omitempty"`
	Duration    int    `json:"duration,omitempty"`   // 视频时长（秒）
	ViewCount   int64  `json:"view_count,omitempty"` // 观看次数
	LikeCount   int64  `json:"like_count,omitempty"` // 点赞数
	Status      string `json:"status,omitempty"`     // 内容状态
}

// BatchAddContentsToAlbumRequest 批量添加内容到合集请求
type BatchAddContentsToAlbumRequest struct {
	UserKSUID  string             `json:"-"` // 从JWT获取
	AlbumKSUID string             `json:"album_ksuid" binding:"required"`
	Contents   []BatchContentItem `json:"contents" binding:"required,min=1,max=50"`
}

// BatchRemoveContentsFromAlbumRequest 批量从合集移除内容请求
type BatchRemoveContentsFromAlbumRequest struct {
	UserKSUID     string   `json:"-"` // 从JWT获取
	AlbumKSUID    string   `json:"album_ksuid" binding:"required"`
	ContentKSUIDs []string `json:"content_ksuids" binding:"required,min=1,max=50"`
}

// BatchContentItem 批量内容项
type BatchContentItem struct {
	ContentKSUID string `json:"content_ksuid" binding:"required"`
	ContentType  string `json:"content_type" binding:"required,oneof=video short_video anime novel"`
	SortOrder    int    `json:"sort_order"`
}

// BatchOperationResponse 批量操作响应
type BatchOperationResponse struct {
	AlbumKSUID   string            `json:"album_ksuid"`
	SuccessCount int               `json:"success_count"`
	FailedCount  int               `json:"failed_count"`
	FailedItems  []BatchFailedItem `json:"failed_items,omitempty"`
	UpdatedAlbum *GetAlbumResponse `json:"updated_album,omitempty"`
}

// BatchFailedItem 批量操作失败项
type BatchFailedItem struct {
	ContentKSUID string `json:"content_ksuid"`
	Error        string `json:"error"`
}

// DeleteAlbumContentsRequest 删除合集所有内容请求
type DeleteAlbumContentsRequest struct {
	UserKSUID  string `json:"-"` // 从JWT获取
	AlbumKSUID string `json:"-"` // 从路径参数获取
}

// GetContentAlbumsRequest 获取内容所属合集列表请求
type GetContentAlbumsRequest struct {
	UserKSUID    string `json:"-"` // 从JWT获取，用于权限检查
	ContentKSUID string `json:"-"` // 从路径参数获取
	Page         int    `json:"page" form:"page" binding:"min=1"`
	PageSize     int    `json:"page_size" form:"page_size" binding:"min=1,max=100"`
}

// GetContentAlbumsResponse 获取内容所属合集列表响应
type GetContentAlbumsResponse struct {
	Albums   []ContentAlbumItem `json:"albums"`
	Total    int64              `json:"total"`
	Page     int                `json:"page"`
	PageSize int                `json:"page_size"`
}

// ContentAlbumItem 内容所属合集项
type ContentAlbumItem struct {
	AlbumKSUID string    `json:"album_ksuid"`
	SortOrder  int       `json:"sort_order"`
	AddedAt    time.Time `json:"added_at"`

	// 合集基本信息
	AlbumName   string `json:"album_name,omitempty"`
	AlbumType   string `json:"album_type,omitempty"`
	IsPublic    bool   `json:"is_public,omitempty"`
	Description string `json:"description,omitempty"`
}

// UpdateContentSortOrderRequest 更新内容在合集中的排序请求
type UpdateContentSortOrderRequest struct {
	UserKSUID    string `json:"-"` // 从JWT获取
	AlbumKSUID   string `json:"album_ksuid" binding:"required"`
	ContentKSUID string `json:"content_ksuid" binding:"required"`
	SortOrder    int    `json:"sort_order" binding:"required"`
}

// CheckContentInAlbumRequest 检查内容是否在合集中请求
type CheckContentInAlbumRequest struct {
	AlbumKSUID   string `json:"album_ksuid" binding:"required"`
	ContentKSUID string `json:"content_ksuid" binding:"required"`
}

// CheckContentInAlbumResponse 检查内容是否在合集中响应
type CheckContentInAlbumResponse struct {
	Exists    bool      `json:"exists"`
	SortOrder int       `json:"sort_order,omitempty"`
	AddedAt   time.Time `json:"added_at,omitempty"`
}
